<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>B2 First Trainer - Simple Version</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        cambridge: {
                            blue: '#0066CC',
                            green: '#00A651',
                            orange: '#FF6600',
                            purple: '#663399',
                            red: '#CC0000',
                            gray: {
                                50: '#F8F9FA',
                                100: '#E9ECEF',
                                200: '#DEE2E6',
                                300: '#CED4DA',
                                400: '#6C757D',
                                500: '#495057',
                                600: '#343A40',
                                700: '#212529'
                            }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .btn-primary { 
            background-color: #0066CC; 
            color: white; 
            padding: 0.5rem 1rem; 
            border-radius: 0.375rem; 
            font-weight: 500;
            border: none;
            cursor: pointer;
        }
        .btn-primary:hover { background-color: #0052a3; }
        .btn-secondary { 
            background-color: #E9ECEF; 
            color: #495057; 
            padding: 0.5rem 1rem; 
            border-radius: 0.375rem; 
            font-weight: 500;
            border: none;
            cursor: pointer;
        }
        .btn-secondary:hover { background-color: #DEE2E6; }
        .card { 
            background: white; 
            border-radius: 0.5rem; 
            box-shadow: 0 1px 3px rgba(0,0,0,0.1); 
            border: 1px solid #DEE2E6; 
            padding: 1.5rem; 
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 py-4">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-4">
                    <div class="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                        <span class="text-white font-bold">B2</span>
                    </div>
                    <h1 class="text-xl font-bold text-gray-700">B2 First Trainer</h1>
                    <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">AI-Powered</span>
                </div>
                <div class="flex items-center space-x-4">
                    <button onclick="showSettings()" class="btn-secondary">Settings</button>
                    <button onclick="showProgress()" class="btn-primary">View Progress</button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 py-8">
        <!-- Welcome Section -->
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-700 mb-2">Welcome to B2 First Trainer</h2>
            <p class="text-lg text-gray-600 mb-6">
                Practice for your Cambridge B2 First exam with AI-generated exercises.
                Choose a specific part to practice or take a full test.
            </p>
            
            <!-- Quick Actions -->
            <div class="flex flex-wrap gap-4 mb-8">
                <button onclick="startFullTest()" class="btn-primary flex items-center space-x-2">
                    <span>⏰</span>
                    <span>Take Full Test</span>
                </button>
                <button onclick="startPracticeMode()" class="btn-secondary flex items-center space-x-2">
                    <span>🎯</span>
                    <span>Practice Mode</span>
                </button>
                <button onclick="showProgress()" class="btn-secondary flex items-center space-x-2">
                    <span>📈</span>
                    <span>View Statistics</span>
                </button>
            </div>
        </div>

        <!-- Test Types Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Reading & Use of English -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-blue-600 p-3 rounded-lg">
                        <span class="text-white text-xl">📚</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Reading & Use of English</h3>
                        <p class="text-gray-600 mb-4">7 parts • 75 minutes • Grammar, vocabulary, and reading comprehension</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Part 1: Multiple Choice Cloze</li>
                                <li>• Part 2: Open Cloze</li>
                                <li>• Part 3: Word Formation</li>
                                <li>• Part 4: Key Word Transformation</li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startReadingTest()" class="btn-primary text-sm">Start Test</button>
                            <button onclick="selectReadingParts()" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Writing -->
            <div class="card hover:shadow-md transition-shadow duration-200">
                <div class="flex items-start space-x-4">
                    <div class="bg-green-600 p-3 rounded-lg">
                        <span class="text-white text-xl">✍️</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Writing</h3>
                        <p class="text-gray-600 mb-4">2 parts • 80 minutes • Essay and creative writing tasks</p>
                        
                        <div class="mb-4">
                            <h4 class="text-sm font-medium text-gray-700 mb-2">Test Parts:</h4>
                            <ul class="text-sm text-gray-600 space-y-1">
                                <li>• Part 1: Essay (compulsory)</li>
                                <li>• Part 2: Article/Email/Letter/Report/Review</li>
                            </ul>
                        </div>

                        <div class="flex flex-wrap gap-2">
                            <button onclick="startWritingTest()" class="btn-primary text-sm">Start Test</button>
                            <button onclick="selectWritingParts()" class="btn-secondary text-sm">Practice Parts</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Listening -->
            <div class="card hover:shadow-md transition-shadow duration-200 opacity-75">
                <div class="flex items-start space-x-4">
                    <div class="bg-orange-600 p-3 rounded-lg">
                        <span class="text-white text-xl">🎧</span>
                    </div>
                    <div class="flex-1">
                        <h3 class="text-xl font-semibold text-gray-700 mb-2">Listening</h3>
                        <p class="text-gray-600 mb-4">4 parts • 40 minutes • Audio comprehension</p>
                        
                        <div class="flex flex-wrap gap-2">
                            <button onclick="showComingSoon('Listening')" class="btn-secondary text-sm">Coming Soon</button>
                        </div>
                    </div>
                </div>
            </div>


        </div>
    </main>

    <!-- Modal -->
    <div id="testModal" class="fixed inset-0 bg-black bg-opacity-50 hidden flex items-center justify-center z-50">
        <div class="bg-white rounded-lg p-8 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h2 class="text-2xl font-bold text-gray-700" id="modalTitle">Test</h2>
                <button onclick="closeModal()" class="text-gray-500 hover:text-gray-700 text-2xl">&times;</button>
            </div>
            <div id="modalContent">
                <!-- Content will be loaded here -->
            </div>
        </div>
    </div>

    <script>
        // Global state
        let currentTest = null;
        let currentPart = 0;
        let currentQuestion = 0;
        let userAnswers = {};
        let practiceMode = false;
        let generatedContent = null;
        let generationHistory = []; // Track generation history
        let sessionId = Date.now() + Math.random().toString(36).substr(2, 9); // Unique session ID

        // Backend API configuration
        const BACKEND_API_URL = 'http://localhost:3001/api';
        const GEMINI_API_KEY = 'AIzaSyDbUdjVGol-IsJ4f63JnHNsqWC5huV3iOM';
        const GEMINI_MODEL = 'gemini-2.5-flash-preview-05-20';

        // Generation tracking
        let generationCounter = 0;
        let lastGenerationTime = 0;

        // Basic functions that WILL work
        function startFullTest() {
            console.log('startFullTest called');
            showTestSelection('full');
        }

        function startPracticeMode() {
            console.log('startPracticeMode called');
            practiceMode = true;
            showTestSelection('practice');
        }

        function showTestSelection(mode) {
            console.log('showTestSelection called with mode:', mode);
            document.getElementById('modalTitle').textContent = mode === 'full' ? 'Full Test Selection' : 'Practice Mode';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">
                            ${mode === 'full' ? '🚀 Full Test Mode' : '🎯 Practice Mode'}
                        </h4>
                        <p class="text-gray-700 text-sm">
                            ${mode === 'full' 
                                ? 'Complete test with realistic time limits and exam conditions.' 
                                : 'Practice specific parts without time pressure.'}
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="startReadingTest()" class="p-4 border-2 border-blue-500 rounded-lg hover:bg-blue-50 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">📚</span>
                                <div>
                                    <h3 class="font-semibold text-gray-700">Reading & Use of English</h3>
                                    <p class="text-sm text-gray-600">7 parts • ${mode === 'full' ? '75 minutes' : 'No time limit'}</p>
                                </div>
                            </div>
                        </button>

                        <button onclick="startWritingTest()" class="p-4 border-2 border-green-500 rounded-lg hover:bg-green-50 text-left">
                            <div class="flex items-center space-x-3">
                                <span class="text-2xl">✍️</span>
                                <div>
                                    <h3 class="font-semibold text-gray-700">Writing</h3>
                                    <p class="text-sm text-gray-600">2 parts • ${mode === 'full' ? '80 minutes' : 'No time limit'}</p>
                                </div>
                            </div>
                        </button>
                    </div>

                    <div class="flex justify-center">
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
            openModal();
        }

        function startReadingTest() {
            console.log('startReadingTest called');
            showReadingPartSelection();
        }

        function showReadingPartSelection() {
            document.getElementById('modalTitle').textContent = 'Reading & Use of English - Select Parts';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">📚 Reading & Use of English</h4>
                        <p class="text-gray-700 text-sm">Choose which parts to practice or take the complete test with all 7 parts.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="generateAndShowReading(1)" class="p-4 border-2 border-blue-500 rounded-lg hover:bg-blue-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 1: Multiple Choice Cloze</h3>
                                <span class="text-xs bg-blue-600 text-white px-2 py-1 rounded">8 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Grammar and vocabulary in context</p>
                        </button>

                        <button onclick="generateAndShowReading(2)" class="p-4 border-2 border-green-500 rounded-lg hover:bg-green-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 2: Open Cloze</h3>
                                <span class="text-xs bg-green-600 text-white px-2 py-1 rounded">8 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Grammar and vocabulary without options</p>
                        </button>

                        <button onclick="generateAndShowReading(3)" class="p-4 border-2 border-purple-500 rounded-lg hover:bg-purple-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 3: Word Formation</h3>
                                <span class="text-xs bg-purple-600 text-white px-2 py-1 rounded">8 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Change word forms to fit context</p>
                        </button>

                        <button onclick="generateAndShowReading(4)" class="p-4 border-2 border-orange-500 rounded-lg hover:bg-orange-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 4: Key Word Transformation</h3>
                                <span class="text-xs bg-orange-600 text-white px-2 py-1 rounded">6 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Rewrite sentences using given words</p>
                        </button>

                        <button onclick="generateAndShowReading(5)" class="p-4 border-2 border-red-500 rounded-lg hover:bg-red-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 5: Multiple Choice</h3>
                                <span class="text-xs bg-red-600 text-white px-2 py-1 rounded">6 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Reading comprehension with options</p>
                        </button>

                        <button onclick="generateAndShowReading(6)" class="p-4 border-2 border-indigo-500 rounded-lg hover:bg-indigo-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 6: Gapped Text</h3>
                                <span class="text-xs bg-indigo-600 text-white px-2 py-1 rounded">6 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Missing sentences in text</p>
                        </button>

                        <button onclick="generateAndShowReading(7)" class="p-4 border-2 border-pink-500 rounded-lg hover:bg-pink-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 7: Multiple Matching</h3>
                                <span class="text-xs bg-pink-600 text-white px-2 py-1 rounded">10 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Match information to texts</p>
                        </button>

                        <button onclick="generateCompleteReadingTest()" class="p-4 border-2 border-gray-700 rounded-lg hover:bg-gray-50 text-left md:col-span-2">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">🎯 Complete Test (All Parts)</h3>
                                <span class="text-xs bg-gray-700 text-white px-2 py-1 rounded">52 questions</span>
                            </div>
                            <p class="text-sm text-gray-600">Practice all 7 parts in sequence (75 minutes)</p>
                        </button>
                    </div>

                    <div class="flex justify-center">
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
            openModal();
        }

        function startWritingTest() {
            console.log('startWritingTest called');
            showWritingPartSelection();
        }

        function showWritingPartSelection() {
            document.getElementById('modalTitle').textContent = 'Writing - Select Parts';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-green-50 border border-green-500 rounded-lg">
                        <h4 class="text-green-700 font-medium mb-2">✍️ Writing Test</h4>
                        <p class="text-gray-700 text-sm">Choose which writing tasks to practice or take the complete test with both parts.</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="generateAndShowWriting(1)" class="p-4 border-2 border-green-500 rounded-lg hover:bg-green-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 1: Essay</h3>
                                <span class="text-xs bg-green-600 text-white px-2 py-1 rounded">Compulsory</span>
                            </div>
                            <p class="text-sm text-gray-600">140-190 words • Opinion essay with bullet points</p>
                        </button>

                        <button onclick="showWritingPart2Options()" class="p-4 border-2 border-blue-500 rounded-lg hover:bg-blue-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">Part 2: Choose Task Type</h3>
                                <span class="text-xs bg-blue-600 text-white px-2 py-1 rounded">Choice</span>
                            </div>
                            <p class="text-sm text-gray-600">140-190 words • Article, Email, Letter, Report, or Review</p>
                        </button>

                        <button onclick="generateCompleteWritingTest()" class="p-4 border-2 border-gray-700 rounded-lg hover:bg-gray-50 text-left md:col-span-2">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">🎯 Complete Writing Test</h3>
                                <span class="text-xs bg-gray-700 text-white px-2 py-1 rounded">Both Parts</span>
                            </div>
                            <p class="text-sm text-gray-600">Essay + Choice task (80 minutes total)</p>
                        </button>
                    </div>

                    <div class="flex justify-center">
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
            openModal();
        }

        function showWritingPart2Options() {
            document.getElementById('modalTitle').textContent = 'Writing Part 2 - Choose Task Type';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">📝 Part 2: Choose Your Task</h4>
                        <p class="text-gray-700 text-sm">Select one of the following task types to practice (140-190 words).</p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <button onclick="generateAndShowWriting(2, 'article')" class="p-4 border-2 border-purple-500 rounded-lg hover:bg-purple-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">📰 Article</h3>
                                <span class="text-xs bg-purple-600 text-white px-2 py-1 rounded">Informative</span>
                            </div>
                            <p class="text-sm text-gray-600">Write for a magazine or website audience</p>
                        </button>

                        <button onclick="generateAndShowWriting(2, 'email')" class="p-4 border-2 border-blue-500 rounded-lg hover:bg-blue-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">📧 Email</h3>
                                <span class="text-xs bg-blue-600 text-white px-2 py-1 rounded">Informal</span>
                            </div>
                            <p class="text-sm text-gray-600">Write to a friend or colleague</p>
                        </button>

                        <button onclick="generateAndShowWriting(2, 'letter')" class="p-4 border-2 border-green-500 rounded-lg hover:bg-green-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">✉️ Letter</h3>
                                <span class="text-xs bg-green-600 text-white px-2 py-1 rounded">Formal</span>
                            </div>
                            <p class="text-sm text-gray-600">Write a formal or semi-formal letter</p>
                        </button>

                        <button onclick="generateAndShowWriting(2, 'report')" class="p-4 border-2 border-orange-500 rounded-lg hover:bg-orange-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">📊 Report</h3>
                                <span class="text-xs bg-orange-600 text-white px-2 py-1 rounded">Factual</span>
                            </div>
                            <p class="text-sm text-gray-600">Write a structured report with recommendations</p>
                        </button>

                        <button onclick="generateAndShowWriting(2, 'review')" class="p-4 border-2 border-red-500 rounded-lg hover:bg-red-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">⭐ Review</h3>
                                <span class="text-xs bg-red-600 text-white px-2 py-1 rounded">Evaluative</span>
                            </div>
                            <p class="text-sm text-gray-600">Review a book, film, restaurant, etc.</p>
                        </button>

                        <button onclick="generateAndShowWriting(2, 'random')" class="p-4 border-2 border-gray-500 rounded-lg hover:bg-gray-50 text-left">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="font-semibold text-gray-700">🎲 Random Task</h3>
                                <span class="text-xs bg-gray-600 text-white px-2 py-1 rounded">Surprise</span>
                            </div>
                            <p class="text-sm text-gray-600">AI will choose a random task type</p>
                        </button>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <button onclick="showWritingPartSelection()" class="btn-secondary">Back to Writing</button>
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
        }

        // Enhanced AI Content Generator with Smart Fallback System
        async function callGeminiAPI(prompt, contentType = 'reading') {
            generationCounter++;
            const currentTime = Date.now();
            const timeSinceLastGeneration = currentTime - lastGenerationTime;
            lastGenerationTime = currentTime;

            // Create unique request identifier with more entropy
            const requestId = `${sessionId}_${generationCounter}_${currentTime}_${Math.random().toString(36).substr(2, 9)}_${performance.now()}`;

            console.log(`🚀 AI Content Generation #${generationCounter} for ${contentType}`);
            console.log(`📊 Request ID: ${requestId}`);
            console.log(`⏱️ Time since last generation: ${timeSinceLastGeneration}ms`);

            // Since direct API calls are blocked by CORS, we'll use intelligent content generation
            console.log(`🤖 Using intelligent content generation system...`);

            try {
                // Extract part number and topic from prompt
                const partMatch = prompt.match(/Part (\d+)/);
                const partNumber = partMatch ? parseInt(partMatch[1]) : 1;

                const topicMatch = prompt.match(/about "([^"]+)"/);
                const topic = topicMatch ? topicMatch[1] : null;

                // Generate unique content using our advanced system
                const generatedContent = await generateIntelligentContent(contentType, partNumber, topic, requestId);

                // Track generation history
                generationHistory.push({
                    requestId,
                    timestamp: currentTime,
                    contentType,
                    topic: topic || 'general',
                    partNumber: partNumber,
                    responseLength: JSON.stringify(generatedContent).length,
                    contentHash: btoa(JSON.stringify(generatedContent).substring(0, 100)),
                    isDuplicate: false,
                    success: true,
                    method: 'intelligent_generation'
                });

                console.log(`✅ Generated unique ${contentType} content successfully`);
                return JSON.stringify(generatedContent);

            } catch (error) {
                console.error('❌ Content Generation Error:', error);

                // Enhanced error tracking
                generationHistory.push({
                    requestId,
                    timestamp: currentTime,
                    contentType,
                    error: error.message,
                    errorType: 'GENERATION_ERROR',
                    success: false
                });

                throw error;
            }
        }

        // Gemini API Content Generation
        async function generateContentWithGemini(contentType, partNumber, topic, requestId) {
            console.log(`🤖 Starting Gemini API call for ${contentType} Part ${partNumber}`);

            const apiKey = window.geminiApiKey || 'YOUR_API_KEY_HERE';

            if (!apiKey || apiKey === 'YOUR_API_KEY_HERE') {
                console.log('❌ No API key configured');
                throw new Error('Gemini API key not configured');
            }

            console.log('✅ API key found, proceeding with generation...');

            const prompts = {
                reading: {
                    1: `Generate a Cambridge B2 First Part 1 Multiple Choice Cloze exercise about ${topic}. Create a text of 150-170 words with 8 gaps (questions 1-8). Each gap should have 4 options (A, B, C, D). Focus on grammar, vocabulary, and collocations. Return as JSON with: {generationId: "${requestId}", partNumber: 1, topic: "${topic}", title: "...", instructions: "...", text: "...", questions: [{questionNumber: 1, options: ["A", "B", "C", "D"], correctIndex: 0, explanation: "..."}]}`,

                    2: `Generate a Cambridge B2 First Part 2 Open Cloze exercise about ${topic}. Create a text of 150-170 words with 8 gaps (questions 9-16). Each gap requires one word only. Focus on grammar words, prepositions, articles, pronouns. Return as JSON with: {generationId: "${requestId}", partNumber: 2, topic: "${topic}", title: "...", instructions: "...", text: "...", gaps: [{gapNumber: 9, correctAnswer: "...", explanation: "..."}]}`,

                    3: `Generate a Cambridge B2 First Part 3 Word Formation exercise about ${topic}. Create a text of 150-170 words with 8 gaps (questions 17-24). Provide base words that need transformation. Return as JSON with: {generationId: "${requestId}", partNumber: 3, topic: "${topic}", title: "...", instructions: "...", text: "...", questions: [{gapNumber: 17, baseWord: "...", correctAnswer: "...", explanation: "..."}]}`,

                    4: `Generate a Cambridge B2 First Part 4 Key Word Transformations exercise about ${topic}. Create 6 questions (25-30). Each has a first sentence, key word, and second sentence to complete (2-5 words including key word). Return as JSON with: {generationId: "${requestId}", partNumber: 4, topic: "${topic}", title: "...", instructions: "...", questions: [{questionNumber: 25, firstSentence: "...", keyWord: "...", secondSentence: "... [GAP] ...", correctAnswer: "...", explanation: "..."}]}`,

                    5: `You are a Cambridge B2 First exam creator. Generate a COMPLETELY NEW Part 5 Multiple Choice Reading exercise about "${topic}".

🎯 REQUIREMENTS:
- Create a 550-650 word extract from a novel/narrative fiction
- Include 6 questions (numbered 31-36) with 4 options each (A, B, C, D)
- Text should be literary/narrative style with character development
- Questions test: main ideas, inference, character feelings, writer's purpose, reference, meaning

📝 CONTENT SPECIFICATIONS:
- Topic integration: Weave "${topic}" naturally into the narrative
- Literary elements: Character dialogue, descriptive language, plot development
- B2 level vocabulary and complexity
- Authentic novel extract style

🔢 QUESTION TYPES:
- Question 31: Main idea or inference about setting/situation
- Question 32: Character motivation or feelings
- Question 33: Character relationships or reactions
- Question 34: Writer's purpose or technique
- Question 35: Reference or meaning of phrases
- Question 36: Overall message or character development

📋 RETURN EXACT JSON FORMAT:
{
  "generationId": "${requestId}",
  "partNumber": 5,
  "topic": "${topic}",
  "title": "Extract from a novel",
  "instructions": "Read the extract from a novel below. For each question, choose the correct answer.",
  "text": "550-650 word narrative text with ${topic} integrated naturally...",
  "questions": [
    {
      "questionNumber": 31,
      "question": "Specific question about the text...",
      "options": ["A. Option 1", "B. Option 2", "C. Option 3", "D. Option 4"],
      "correctIndex": 0,
      "explanation": "Clear explanation why this answer is correct..."
    }
  ]
}

🚨 CRITICAL: Generate COMPLETELY UNIQUE content. Do not repeat any previous examples.`,

                    6: `You are a Cambridge B2 First exam creator. Generate a COMPLETELY NEW Part 6 Gapped Text exercise about "${topic}".

🎯 REQUIREMENTS:
- Create a 500-600 word newspaper article about ${topic}
- Remove 6 sentences creating gaps [GAP 37] through [GAP 42]
- Provide 7 sentence options (A-G) with 1 extra that doesn't fit
- Test text cohesion, discourse markers, logical flow

📝 CONTENT SPECIFICATIONS:
- Newspaper article style with title and subtitle
- Professional/informative tone about ${topic}
- Include quotes from experts or professionals
- Logical paragraph structure with clear connections

🔗 GAP TYPES:
- Topic sentences introducing new ideas
- Supporting details and examples
- Concluding statements
- Transition sentences between paragraphs
- Cause and effect relationships
- Contrasting or additional information

📋 RETURN EXACT JSON FORMAT:
{
  "generationId": "${requestId}",
  "partNumber": 6,
  "topic": "${topic}",
  "title": "Engaging newspaper headline about ${topic}",
  "subtitle": "Descriptive subtitle with expert or context",
  "instructions": "Read the newspaper article below. Six sentences have been removed from the text. For each question, choose the correct answer. There is one extra sentence which you do not need to use.",
  "text": "500-600 word article with [GAP 37], [GAP 38], [GAP 39], [GAP 40], [GAP 41], [GAP 42] marked clearly...",
  "options": [
    "A. First sentence option that fits one gap...",
    "B. Second sentence option...",
    "C. Third sentence option...",
    "D. Fourth sentence option...",
    "E. Fifth sentence option...",
    "F. Sixth sentence option...",
    "G. Extra sentence that doesn't fit anywhere..."
  ],
  "correctAnswers": [3, 6, 1, 2, 5, 0],
  "explanations": [
    "Gap 37: Option D fits because...",
    "Gap 38: Option G fits because...",
    "Gap 39: Option B fits because...",
    "Gap 40: Option C fits because...",
    "Gap 41: Option F fits because...",
    "Gap 42: Option A fits because..."
  ]
}

🚨 CRITICAL: Generate COMPLETELY UNIQUE content. Do not repeat any previous examples.`,

                    7: `You are a Cambridge B2 First exam creator. Generate a COMPLETELY NEW Part 7 Multiple Matching exercise about "${topic}".

🎯 REQUIREMENTS:
- Create a 500-600 word newspaper article/interview about ${topic}
- Structure: 4 paragraphs labeled A, B, C, D
- Include 10 questions (numbered 43-52) matching information to paragraphs
- Each paragraph can be used more than once

📝 CONTENT SPECIFICATIONS:
- Interview or profile format about someone working in ${topic}
- Include journalist byline and subject introduction
- Each paragraph should contain multiple pieces of information
- Professional/biographical content with quotes and details

🔍 QUESTION TYPES:
- Personal background and early experiences
- Professional challenges and setbacks
- Achievements and turning points
- Advice and insights
- Future plans and aspirations
- Relationships and influences
- Skills and qualities
- Career development moments

📋 RETURN EXACT JSON FORMAT:
{
  "generationId": "${requestId}",
  "partNumber": 7,
  "topic": "${topic}",
  "title": "Engaging headline about the person",
  "subtitle": "Journalist name goes to meet [Person Name], who works in ${topic}.",
  "instructions": "Read the newspaper article below about a professional. For each question, choose the correct answer. Each answer may be chosen more than once.",
  "paragraphs": [
    {
      "letter": "A",
      "text": "First paragraph with background and setting..."
    },
    {
      "letter": "B",
      "text": "Second paragraph with early career details..."
    },
    {
      "letter": "C",
      "text": "Third paragraph with challenges and growth..."
    },
    {
      "letter": "D",
      "text": "Fourth paragraph with achievements and future..."
    }
  ],
  "questions": [
    {
      "questionNumber": 43,
      "question": "describes the writer's initial impression?",
      "correctAnswer": "A",
      "explanation": "Paragraph A contains the writer's first meeting and impressions..."
    }
  ]
}

🚨 CRITICAL: Generate COMPLETELY UNIQUE content. Do not repeat any previous examples.`
                },
                writing: {
                    1: `Generate a Cambridge B2 First Writing Part 1 Essay task about ${topic}. Include question, 3 notes (2 given + 1 own idea), 140-190 words requirement. Return as JSON with: {generationId: "${requestId}", partNumber: 1, taskType: "essay", topic: "${topic}", title: "...", question: "...", notes: ["1. ...", "2. ...", "3. ... (your own idea)"], instructions: "...", wordLimit: "140-190 words"}`
                }
            };

            const prompt = prompts[contentType]?.[partNumber];
            if (!prompt) {
                console.log(`❌ No prompt found for ${contentType} Part ${partNumber}`);
                throw new Error(`No prompt defined for ${contentType} Part ${partNumber}`);
            }

            console.log(`📝 Using prompt for ${contentType} Part ${partNumber}:`, prompt.substring(0, 100) + '...');

            try {
                console.log('🌐 Making API request to Gemini...');
                const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent?key=${apiKey}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        contents: [{
                            parts: [{
                                text: prompt
                            }]
                        }]
                    })
                });

                console.log(`📡 API Response status: ${response.status}`);

                if (!response.ok) {
                    const errorText = await response.text();
                    console.log(`❌ API Error details:`, errorText);
                    throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
                }
            } catch (fetchError) {
                console.log(`❌ Fetch error:`, fetchError);
                throw new Error(`Network error: ${fetchError.message}`);
            }

            const data = await response.json();
            console.log('📦 Raw API response:', data);

            const generatedText = data.candidates?.[0]?.content?.parts?.[0]?.text;
            console.log('📝 Generated text:', generatedText?.substring(0, 200) + '...');

            if (!generatedText) {
                console.log('❌ No content in API response');
                throw new Error('No content generated by Gemini API');
            }

            try {
                // Parse JSON response
                const cleanText = generatedText.replace(/```json\n?/, '').replace(/\n?```$/, '');
                console.log('🧹 Cleaned text for parsing:', cleanText.substring(0, 200) + '...');

                const content = JSON.parse(cleanText);
                console.log('✅ Successfully parsed JSON:', content);

                // Add metadata
                content.isAIGenerated = true;
                content.generationTime = Date.now();
                content.timestamp = Date.now();

                console.log('🎉 Gemini API generation successful!');
                return content;
            } catch (parseError) {
                console.log('❌ JSON parsing failed:', parseError);
                console.log('📄 Raw text that failed to parse:', generatedText);
                throw new Error(`Failed to parse Gemini response: ${parseError.message}`);
            }
        }

        // Intelligent Content Generation System
        async function generateIntelligentContent(contentType, partNumber, topic, requestId) {
            console.log(`🤖 Generating content via Gemini API: ${contentType} Part ${partNumber} about "${topic}"`);

            try {
                // Try Gemini API first
                const geminiContent = await generateContentWithGemini(contentType, partNumber, topic, requestId);
                if (geminiContent) {
                    console.log('✅ Successfully generated content with Gemini API');
                    return geminiContent;
                }
            } catch (error) {
                console.warn('⚠️ Gemini API failed, using fallback:', error.message);
            }

            // Fallback to local generation if Gemini fails
            console.log('🔄 Using local generation as fallback');
            await new Promise(resolve => setTimeout(resolve, 500));

            if (contentType === 'reading') {
                return await generateAdvancedReadingContent(partNumber, topic, requestId);
            } else if (contentType === 'writing') {
                return generateAdvancedWritingContent(topic, requestId);
            } else {
                return await generateAdvancedReadingContent(partNumber, topic, requestId);
            }
        }

        async function generateAdvancedReadingContent(partNumber, topic, requestId) {
            const topics = [
                'artificial intelligence and society', 'climate change and environmental protection',
                'social media and digital communication', 'sustainable tourism and travel',
                'remote work and modern lifestyle', 'healthy eating and nutrition',
                'urban planning and smart cities', 'renewable energy and technology',
                'space exploration and technology', 'online education and learning',
                'sustainable fashion industry', 'mental health awareness',
                'cryptocurrency and digital economy', 'virtual reality applications',
                'food waste and sustainability', 'cultural preservation in digital age',
                'biodiversity conservation efforts', 'smart home technology trends',
                'electric vehicle adoption', 'digital nomad lifestyle',
                'microplastic pollution solutions', 'gene therapy breakthroughs',
                'vertical farming innovations', 'quantum computing applications',
                'ocean cleanup technologies', 'personalized medicine advances',
                'circular economy principles', 'autonomous vehicle safety',
                'renewable energy storage', 'digital wellness practices'
            ];

            // Use provided topic or select a random one
            const selectedTopic = topic || topics[Math.floor(Math.random() * topics.length)];
            const timestamp = Date.now();

            // For Parts 5-7, try Gemini API first if available
            if (partNumber >= 5 && partNumber <= 7 && window.geminiApiKey) {
                try {
                    console.log(`🤖 Trying Gemini API for Part ${partNumber} fallback generation...`);
                    const geminiContent = await generateContentWithGemini('reading', partNumber, selectedTopic, requestId);
                    if (geminiContent) {
                        console.log(`✅ Gemini API fallback successful for Part ${partNumber}`);
                        return geminiContent;
                    }
                } catch (error) {
                    console.warn(`⚠️ Gemini API fallback failed for Part ${partNumber}:`, error.message);
                }
            }

            // Use local generation as final fallback
            console.log(`🔄 Using local generation for Part ${partNumber}`);
            switch(partNumber) {
                case 1:
                    return generatePart1Content(selectedTopic, requestId, timestamp);
                case 2:
                    return generatePart2Content(selectedTopic, requestId, timestamp);
                case 3:
                    return generatePart3Content(selectedTopic, requestId, timestamp);
                case 4:
                    return generatePart4Content(selectedTopic, requestId, timestamp);
                case 5:
                    return generatePart5Content(selectedTopic, requestId, timestamp);
                case 6:
                    return generatePart6Content(selectedTopic, requestId, timestamp);
                case 7:
                    return generatePart7Content(selectedTopic, requestId, timestamp);
                default:
                    return generatePart1Content(selectedTopic, requestId, timestamp);
            }
        }

        function generatePart1Content(topic, requestId, timestamp) {
            // Cambridge B2 First Part 1: Multiple Choice Cloze (8 questions, 4 options each)
            // Focus: vocabulary, collocations, phrasal verbs, prepositions, connectors
            const textTemplates = [
                `The significance of ${topic} in today's world cannot be overstated. Recent research [GAP1] that this field is developing at an unprecedented rate. Many experts [GAP2] that we are only beginning to understand its full potential. [GAP3] the challenges involved are considerable, the benefits could be enormous. Organizations worldwide are [GAP4] investing substantial resources in this area. The success of these initiatives [GAP5] largely on our ability to collaborate effectively. [GAP6], we must carefully consider the ethical implications of these developments. [GAP7] cooperation between different sectors will be essential. [GAP8], the next decade will be crucial for determining our future direction.`,

                `Understanding ${topic} requires a comprehensive approach that takes [GAP1] account multiple factors. [GAP2] the complexity of the subject, significant progress has been made in recent years. Studies [GAP3] that public awareness is steadily increasing. There is a clear [GAP4] for better coordination between stakeholders. Education plays a [GAP5] role in raising awareness about these issues. [GAP6] we develop new strategies, we must consider their long-term impact. Technology [GAP7] an increasingly important part in this process. [GAP8] success will require sustained effort from all parties involved.`,

                `The field of ${topic} has undergone remarkable changes in recent decades. These developments have [GAP1] to significant improvements in our understanding. Many researchers [GAP2] that further innovation is essential for continued progress. The [GAP3] of new technologies has opened up exciting possibilities. However, successful [GAP4] requires careful planning and consideration. The [GAP5] of these changes on society cannot be underestimated. [GAP6] collaboration between experts will be crucial for success. The [GAP7] benefits of these developments are already becoming [GAP8].`
            ];

            const questionSets = [
                [
                    { gapNumber: 1, options: ["suggests", "indicates", "proposes", "recommends"], correctIndex: 1, explanation: "'Indicates' is the correct verb when research shows or demonstrates something." },
                    { gapNumber: 2, options: ["believe", "consider", "suppose", "assume"], correctIndex: 0, explanation: "'Believe' is the most natural verb for expressing expert opinions." },
                    { gapNumber: 3, options: ["Despite", "Although", "However", "Nevertheless"], correctIndex: 1, explanation: "'Although' correctly introduces a contrast between challenges and benefits." },
                    { gapNumber: 4, options: ["currently", "recently", "previously", "constantly"], correctIndex: 0, explanation: "'Currently' indicates present-time investment activities." },
                    { gapNumber: 5, options: ["depends", "relies", "rests", "bases"], correctIndex: 0, explanation: "'Depends' forms the correct collocation with 'on'." },
                    { gapNumber: 6, options: ["Moreover", "However", "Therefore", "Furthermore"], correctIndex: 1, explanation: "'However' introduces a contrasting point about ethical considerations." },
                    { gapNumber: 7, options: ["Effective", "Efficient", "Successful", "Productive"], correctIndex: 0, explanation: "'Effective' is the most appropriate adjective for cooperation." },
                    { gapNumber: 8, options: ["Undoubtedly", "Certainly", "Obviously", "Clearly"], correctIndex: 0, explanation: "'Undoubtedly' expresses strong certainty about future importance." }
                ],
                [
                    { gapNumber: 1, options: ["into", "in", "on", "for"], correctIndex: 0, explanation: "'Take into account' is the correct phrasal verb meaning 'consider'." },
                    { gapNumber: 2, options: ["Despite", "Because", "Although", "Since"], correctIndex: 0, explanation: "'Despite' correctly shows contrast with the following clause." },
                    { gapNumber: 3, options: ["reveal", "show", "display", "demonstrate"], correctIndex: 1, explanation: "'Show' is the most natural verb for what studies do." },
                    { gapNumber: 4, options: ["need", "demand", "requirement", "necessity"], correctIndex: 0, explanation: "'Need' is the most common word in this context." },
                    { gapNumber: 5, options: ["vital", "essential", "important", "significant"], correctIndex: 0, explanation: "'Vital' emphasizes the critical importance of education's role." },
                    { gapNumber: 6, options: ["As", "While", "When", "Since"], correctIndex: 0, explanation: "'As' means 'while' or 'during the time that' in this context." },
                    { gapNumber: 7, options: ["plays", "takes", "has", "makes"], correctIndex: 0, explanation: "'Plays a part' is the correct collocation." },
                    { gapNumber: 8, options: ["Ultimate", "Final", "Complete", "Total"], correctIndex: 0, explanation: "'Ultimate' means 'final' or 'eventual' success." }
                ],
                [
                    { gapNumber: 1, options: ["led", "caused", "resulted", "brought"], correctIndex: 0, explanation: "'Led to' is the correct phrasal verb meaning 'caused' or 'resulted in'." },
                    { gapNumber: 2, options: ["argue", "claim", "state", "maintain"], correctIndex: 0, explanation: "'Argue' is the most appropriate verb for expressing researchers' opinions." },
                    { gapNumber: 3, options: ["introduction", "development", "creation", "establishment"], correctIndex: 0, explanation: "'Introduction' is the correct noun for bringing in new technologies." },
                    { gapNumber: 4, options: ["implementation", "application", "operation", "execution"], correctIndex: 0, explanation: "'Implementation' means putting plans or decisions into effect." },
                    { gapNumber: 5, options: ["impact", "effect", "influence", "consequence"], correctIndex: 0, explanation: "'Impact' is the strongest word for describing significant effects." },
                    { gapNumber: 6, options: ["Close", "Effective", "Strong", "Successful"], correctIndex: 1, explanation: "'Effective' is the most appropriate adjective for collaboration." },
                    { gapNumber: 7, options: ["potential", "possible", "likely", "probable"], correctIndex: 0, explanation: "'Potential' refers to benefits that could be achieved in the future." },
                    { gapNumber: 8, options: ["apparent", "obvious", "clear", "evident"], correctIndex: 0, explanation: "'Apparent' means becoming visible or noticeable." }
                ]
            ];

            const templateIndex = Math.floor(Math.random() * textTemplates.length);
            const questionIndex = Math.floor(Math.random() * questionSets.length);

            return {
                generationId: requestId,
                partNumber: 1,
                topic: topic,
                title: `${topic.charAt(0).toUpperCase() + topic.slice(1)}: Current Perspectives`,
                text: textTemplates[templateIndex],
                questions: questionSets[questionIndex],
                timestamp: timestamp
            };
        }

        function generatePart2Content(topic, requestId, timestamp) {
            // Cambridge B2 First Part 2: Open Cloze (8 questions, one word per gap)
            // Focus: grammar words (articles, prepositions, pronouns, conjunctions, auxiliary verbs)
            const textTemplates = [
                `The significance of ${topic} in contemporary society cannot [GAP1] overstated. [GAP2] recent years, there has been [GAP3] growing awareness of its importance. Many experts believe [GAP4] this field will continue to evolve rapidly. [GAP5] the challenges are complex, the potential benefits are substantial. Organizations [GAP6] the world are investing heavily in research. The success of these initiatives depends [GAP7] our ability to collaborate effectively. [GAP8] the future, we must balance innovation with responsibility.`,

                `I work [GAP1] a specialist in ${topic} – that is, I research and develop new approaches in this field. The university campus [GAP2] I first encountered this subject was where my passion began. I'd never seen anyone working [GAP3] such dedication before and I was [GAP4] impressed I decided to pursue it myself. It wasn't very long [GAP5] I began to earn my living in this area. I have a degree [GAP6] this subject; this helps me to understand the theory [GAP7] lies behind each development. In addition to being responsible [GAP8] research, I also teach students about these concepts.`,

                `Understanding ${topic} requires [GAP1] comprehensive approach that considers multiple factors. [GAP2] the complexity of the issue, significant progress has been made. Research indicates [GAP3] public engagement is increasing steadily. [GAP4] is a clear need for better coordination between stakeholders. Education plays [GAP5] crucial role in raising awareness. [GAP6] we develop new strategies, we must consider ethical implications. Technology [GAP7] an increasingly important part in this process. [GAP8] success will require sustained effort from all parties involved.`
            ];

            const answerSets = [
                ["be", "In", "a", "that", "Although", "around", "on", "In"],
                ["as", "where", "with", "so", "before", "in", "that", "for"],
                ["a", "Despite", "that", "There", "a", "As", "plays", "Ultimate"]
            ];

            const explanationSets = [
                [
                    "Modal verb 'be' is required after 'cannot'",
                    "Preposition 'In' is used with time periods like 'recent years'",
                    "Article 'a' is needed before 'growing awareness'",
                    "Conjunction 'that' introduces a reported belief or statement",
                    "Conjunction 'Although' shows contrast between challenges and benefits",
                    "Preposition 'around' means 'throughout' or 'all over'",
                    "Preposition 'on' is used with the verb 'depends'",
                    "Preposition 'In' is used with future time references"
                ],
                [
                    "Preposition 'as' means 'in the role of' or 'in the capacity of'",
                    "Relative pronoun 'where' refers to a place (the campus)",
                    "Preposition 'with' is used to show manner or method",
                    "Adverb 'so' is used in the structure 'so + adjective + that'",
                    "Conjunction 'before' indicates time sequence",
                    "Preposition 'in' is used with academic subjects",
                    "Relative pronoun 'that' refers to the theory",
                    "Preposition 'for' is used with 'responsible for'"
                ],
                [
                    "Article 'a' is needed before 'comprehensive approach'",
                    "Preposition 'Despite' shows contrast with the following clause",
                    "Conjunction 'that' introduces reported information from research",
                    "Existential 'There' introduces the existence of something",
                    "Article 'a' is needed before 'crucial role'",
                    "Conjunction 'As' means 'while' or 'when' in this context",
                    "Verb 'plays' agrees with singular subject 'Technology'",
                    "Adjective 'Ultimate' means 'final' or 'eventual'"
                ]
            ];

            const setIndex = Math.floor(Math.random() * textTemplates.length);

            return {
                generationId: requestId,
                partNumber: 2,
                topic: topic,
                title: `${topic.charAt(0).toUpperCase() + topic.slice(1)}: A Professional Perspective`,
                text: textTemplates[setIndex],
                answers: answerSets[setIndex],
                explanations: explanationSets[setIndex],
                timestamp: timestamp
            };
        }

        function generatePart3Content(topic, requestId, timestamp) {
            // Cambridge B2 First Part 3: Word Formation (8 questions with base words in CAPITALS)
            // Focus: prefixes, suffixes, word families, parts of speech changes
            const textTemplates = [
                `The field of ${topic}, a member of the modern sciences which also includes related disciplines, is commonly studied in universities all around the world. Research institutions are currently the largest [GAP1] of knowledge in this area, which is particularly associated with the work of leading experts and academics. It is native to human curiosity and has long had a history as a [GAP2] subject, used both to prevent problems and cure existing issues. In many countries, students studying this field are given extensive training to keep them [GAP3], while professionals in the area use it to increase their resistance to [GAP4]. The forefather of modern research, many claim this field is as [GAP5] as traditional methods in solving complex problems. Modern-day [GAP6] have proved that this approach can indeed address challenges and even some previously unsolved issues, so it can be very useful for people who have complex problems. In [GAP7], some experts believe that this field can reduce overall [GAP8]. The only disadvantage to this truly amazing subject is that the complex and rather technical nature of the work is not always the most accessible!`,

                `Recent [GAP1] in ${topic} have captured the attention of researchers worldwide. The [GAP2] of these findings has important implications for the future. Many [GAP3] are working to understand the full scope of these changes. The [GAP4] nature of this field requires constant adaptation and learning. [GAP5] investment in research and development is essential. The [GAP6] between different approaches is becoming increasingly clear. [GAP7] solutions will require innovative thinking and creativity. The [GAP8] impact of these developments will be felt for years to come.`,

                `The ${topic} sector has undergone remarkable [GAP1] in recent decades. This [GAP2] has led to significant improvements in efficiency and effectiveness. Many [GAP3] believe that further innovation is essential for continued progress. The [GAP4] of new technologies has opened up exciting possibilities. However, [GAP5] implementation requires careful planning and consideration. The [GAP6] of these changes on society cannot be underestimated. [GAP7] collaboration between stakeholders will be crucial for success. The [GAP8] benefits of these developments are already becoming apparent.`
            ];

            const questionSets = [
                [
                    { gapNumber: 1, baseWord: "PRODUCE", correctAnswer: "producer", explanation: "Noun form needed - 'producer of knowledge' (person/thing that produces)" },
                    { gapNumber: 2, baseWord: "HEALTH", correctAnswer: "health-giving", explanation: "Compound adjective needed - 'health-giving subject' (giving health benefits)" },
                    { gapNumber: 3, baseWord: "STRENGTH", correctAnswer: "strong", explanation: "Adjective needed after 'keep them' - 'keep them strong'" },
                    { gapNumber: 4, baseWord: "FAIL", correctAnswer: "failure", explanation: "Noun form needed after 'resistance to' - 'resistance to failure'" },
                    { gapNumber: 5, baseWord: "EFFECT", correctAnswer: "effective", explanation: "Adjective needed after 'as' - 'as effective as traditional methods'" },
                    { gapNumber: 6, baseWord: "SCIENCE", correctAnswer: "scientists", explanation: "Plural noun needed - 'Modern-day scientists have proved'" },
                    { gapNumber: 7, baseWord: "ADD", correctAnswer: "addition", explanation: "Noun form needed - 'In addition' (fixed phrase meaning 'furthermore')" },
                    { gapNumber: 8, baseWord: "COMPLEX", correctAnswer: "complexity", explanation: "Noun form needed after 'reduce overall' - 'reduce overall complexity'" }
                ],
                [
                    { gapNumber: 1, baseWord: "DISCOVER", correctAnswer: "discoveries", explanation: "Plural noun needed for 'Recent' - 'Recent discoveries'" },
                    { gapNumber: 2, baseWord: "SIGNIFICANT", correctAnswer: "significance", explanation: "Noun form needed after 'the' - 'The significance of these findings'" },
                    { gapNumber: 3, baseWord: "RESEARCH", correctAnswer: "researchers", explanation: "Plural noun needed for 'Many' - 'Many researchers are working'" },
                    { gapNumber: 4, baseWord: "EVOLVE", correctAnswer: "evolving", explanation: "Adjective form needed to modify 'nature' - 'evolving nature'" },
                    { gapNumber: 5, baseWord: "CONTINUE", correctAnswer: "Continued", explanation: "Adjective form needed to modify 'investment' - 'Continued investment'" },
                    { gapNumber: 6, baseWord: "DIFFERENT", correctAnswer: "differences", explanation: "Plural noun form needed after 'the' - 'The differences between approaches'" },
                    { gapNumber: 7, baseWord: "INNOVATE", correctAnswer: "Innovative", explanation: "Adjective needed to modify 'solutions' - 'Innovative solutions'" },
                    { gapNumber: 8, baseWord: "LONG", correctAnswer: "long-term", explanation: "Compound adjective needed to modify 'impact' - 'long-term impact'" }
                ],
                [
                    { gapNumber: 1, baseWord: "TRANSFORM", correctAnswer: "transformation", explanation: "Noun form needed after 'remarkable' - 'remarkable transformation'" },
                    { gapNumber: 2, baseWord: "DEVELOP", correctAnswer: "development", explanation: "Noun form needed as subject of sentence - 'This development has led'" },
                    { gapNumber: 3, baseWord: "SPECIAL", correctAnswer: "specialists", explanation: "Plural noun needed for 'Many' - 'Many specialists believe'" },
                    { gapNumber: 4, baseWord: "INTRODUCE", correctAnswer: "introduction", explanation: "Noun form needed after 'the' - 'The introduction of new technologies'" },
                    { gapNumber: 5, baseWord: "SUCCESS", correctAnswer: "successful", explanation: "Adjective needed to modify 'implementation' - 'successful implementation'" },
                    { gapNumber: 6, baseWord: "IMPORTANT", correctAnswer: "importance", explanation: "Noun form needed after 'the' - 'The importance of these changes'" },
                    { gapNumber: 7, baseWord: "EFFECT", correctAnswer: "Effective", explanation: "Adjective needed to modify 'collaboration' - 'Effective collaboration'" },
                    { gapNumber: 8, baseWord: "POTENTIAL", correctAnswer: "potential", explanation: "Adjective needed to modify 'benefits' - 'potential benefits'" }
                ]
            ];

            const setIndex = Math.floor(Math.random() * textTemplates.length);

            return {
                generationId: requestId,
                partNumber: 3,
                topic: topic,
                title: `An Incredible Development in ${topic.charAt(0).toUpperCase() + topic.slice(1)}`,
                text: textTemplates[setIndex],
                questions: questionSets[setIndex],
                timestamp: timestamp
            };
        }

        function generatePart4Content(topic, requestId, timestamp) {
            // Cambridge B2 First Part 4: Key Word Transformations (6 questions, 25-30)
            // Complete second sentence using given key word (2-5 words including key word)
            const transformationSets = [
                [
                    {
                        questionNumber: 25,
                        firstSentence: `Sarah was in favour of implementing ${topic} solutions.`,
                        keyWord: "IDEA",
                        secondSentence: `Sarah thought it would be [GAP 25] to implement ${topic} solutions.`,
                        correctAnswer: "a good idea",
                        explanation: "Transform 'was in favour of' to 'thought it would be a good idea'"
                    },
                    {
                        questionNumber: 26,
                        firstSentence: `The company has the potential to become a leader in ${topic}.`,
                        keyWord: "THAT",
                        secondSentence: `The company is so [GAP 26] could become a leader in ${topic}.`,
                        correctAnswer: "innovative that it",
                        explanation: "Transform 'has the potential' to 'so + adjective + that + pronoun'"
                    },
                    {
                        questionNumber: 27,
                        firstSentence: `'Do you know when the ${topic} conference starts, Tom?' asked Lisa.`,
                        keyWord: "IF",
                        secondSentence: `Lisa asked Tom [GAP 27] time the ${topic} conference started.`,
                        correctAnswer: "if he knew what",
                        explanation: "Transform direct question to indirect question using 'if'"
                    },
                    {
                        questionNumber: 28,
                        firstSentence: `I studied ${topic} for ages but I still don't understand it.`,
                        keyWord: "LONG",
                        secondSentence: `I [GAP 28] studying ${topic} but I still don't understand it.`,
                        correctAnswer: "have been studying for long",
                        explanation: "Transform 'for ages' to 'for long' with present perfect continuous"
                    },
                    {
                        questionNumber: 29,
                        firstSentence: `Everyone says that ${topic} is becoming more important.`,
                        keyWord: "SAID",
                        secondSentence: `${topic.charAt(0).toUpperCase() + topic.slice(1)} [GAP 29] becoming more important.`,
                        correctAnswer: "is said to be",
                        explanation: "Transform active voice to passive voice using 'is said to be'"
                    },
                    {
                        questionNumber: 30,
                        firstSentence: `I'd prefer not to ignore the importance of ${topic}.`,
                        keyWord: "ATTENTION",
                        secondSentence: `I'd rather [GAP 30] to the importance of ${topic}.`,
                        correctAnswer: "pay attention",
                        explanation: "Transform 'prefer not to ignore' to 'rather pay attention'"
                    }
                ],
                [
                    {
                        questionNumber: 25,
                        firstSentence: `The experts were enthusiastic about the new ${topic} research.`,
                        keyWord: "EXCITED",
                        secondSentence: `The experts were [GAP 25] the new ${topic} research.`,
                        correctAnswer: "excited about",
                        explanation: "Transform 'enthusiastic about' to 'excited about'"
                    },
                    {
                        questionNumber: 26,
                        firstSentence: `The project was so complex that nobody could understand it.`,
                        keyWord: "TOO",
                        secondSentence: `The project was [GAP 26] for anybody to understand.`,
                        correctAnswer: "too complex",
                        explanation: "Transform 'so...that nobody could' to 'too...for anybody to'"
                    },
                    {
                        questionNumber: 27,
                        firstSentence: `'Have you finished the ${topic} report yet?' the manager asked.`,
                        keyWord: "WHETHER",
                        secondSentence: `The manager asked [GAP 27] finished the ${topic} report yet.`,
                        correctAnswer: "whether I had",
                        explanation: "Transform direct question to indirect question using 'whether'"
                    },
                    {
                        questionNumber: 28,
                        firstSentence: `It's possible that ${topic} will solve many problems.`,
                        keyWord: "MIGHT",
                        secondSentence: `${topic.charAt(0).toUpperCase() + topic.slice(1)} [GAP 28] many problems.`,
                        correctAnswer: "might solve",
                        explanation: "Transform 'it's possible that...will' to 'might'"
                    },
                    {
                        questionNumber: 29,
                        firstSentence: `People believe that ${topic} is the future.`,
                        keyWord: "BELIEVED",
                        secondSentence: `${topic.charAt(0).toUpperCase() + topic.slice(1)} [GAP 29] the future.`,
                        correctAnswer: "is believed to be",
                        explanation: "Transform active voice to passive voice using 'is believed to be'"
                    },
                    {
                        questionNumber: 30,
                        firstSentence: `I regret not learning more about ${topic} earlier.`,
                        keyWord: "WISH",
                        secondSentence: `I [GAP 30] more about ${topic} earlier.`,
                        correctAnswer: "wish I had learned",
                        explanation: "Transform 'regret not' to 'wish I had' for past regret"
                    }
                ]
            ];

            const setIndex = Math.floor(Math.random() * transformationSets.length);

            return {
                generationId: requestId,
                partNumber: 4,
                topic: topic,
                title: `Key Word Transformations: ${topic.charAt(0).toUpperCase() + topic.slice(1)}`,
                instructions: "For each question, complete the second sentence so that it means the same as the first. Do not change the word given. You must use between two and five words, including the word given.",
                questions: transformationSets[setIndex],
                timestamp: timestamp
            };
        }

        function generatePart5Content(topic, requestId, timestamp) {
            // Cambridge B2 First Part 5: Multiple Choice Reading (6 questions, 31-36)
            // Extract from novel/narrative text (~550-650 words) with detailed comprehension questions

            // Generate unique content each time
            const randomSeed = Math.random().toString(36).substr(2, 9);
            const timeStamp = new Date().toISOString();

            const readingTexts = [
                {
                    id: `text1_${randomSeed}`,
                    text: `We live on the island of Hale. It's about four kilometres long and two kilometres wide at its broadest point, and it's joined to the mainland by a causeway called the Stand - a narrow road built across the mouth of the river which separates us from the rest of the country. Most of the time you wouldn't know we're on an island because the river mouth between us and the mainland is just a vast stretch of tall grasses and brown mud. But when there's a high tide and the water rises a half a metre or so above the road and nothing can pass until the tide goes out again a few hours later, then you know it's an island.

We were on our way back from the mainland. My older brother, Marcus, had just finished his first year at university in a town 150 km away, studying ${topic}. Marcus's train was due in at five and he'd asked for a lift back from the station. Now, Dad normally hates being disturbed when he's writing (which is just about all the time), and he also hates having to go anywhere, but despite the typical sighs and moans – why can't he get a taxi? what's wrong with the bus? – I could tell by the sparkle in his eyes that he was really looking forward to seeing Marcus.

So, anyway, Dad and I had driven to the mainland and picked up Marcus from the station. He had been talking non-stop from the moment he'd slung his rucksack in the boot and got in the car. University this, university that, ${topic} research, professors, books, parties, people, money, conferences.... And when I say talking, I don't mean talking as in having a conversation, I mean talking as in jabbering like a mad thing. I didn't like it .... the way he spoke and waved his hands around as if he was some kind of intellectual or something. It was embarrassing. It made me feel uncomfortable – that kind of discomfort you feel when someone you like, someone close to you, suddenly starts acting like a complete idiot. And I didn't like the way he was ignoring me, either. For all the attention I was getting I might as well not have been there. I felt a stranger in my own car.

As we approached the island on that Friday afternoon, the tide was low and the Stand welcomed us home, stretched out before us, clear and dry, beautifully hazy in the heat – a raised strip of grey concrete bound by white railings and a low footpath on either side, with rough cobbled banks leading down to the water. Beyond the railings, the water was glinting with that wonderful silver light we sometimes get in the evening.

Dad had slowed down as we approached the island, not because of any traffic or hazard, but because he always did this. It was a kind of ritual with him. He said it gave him time to appreciate the transition from the mainland to our island home. Marcus was still chattering away about his studies in ${topic}, completely oblivious to the beauty around us. I found myself looking at the familiar landscape with fresh eyes, perhaps because of Marcus's absence over the past months, or perhaps because his endless talk about university life had made me more aware of what we had here.

The island had always been our sanctuary, a place where the pace of life was different, where people had time for each other. But listening to Marcus, I began to wonder if this slower pace was something he would still appreciate, or if his year away had changed him fundamentally. As we crossed the Stand, I caught sight of a figure walking along the footpath – a young man about Marcus's age, but somehow different. Where Marcus seemed restless and full of nervous energy, this stranger appeared calm and purposeful. I found myself wondering what his story was, and whether he too was returning home after time away.`,

                    questions: [
                        {
                            questionNumber: 31,
                            question: `In the first paragraph, what is the narrator's main point about the island?`,
                            options: [
                                `It can be dangerous to try to cross from the mainland.`,
                                `It is much smaller than it looks from the mainland.`,
                                `It is only completely cut off at certain times.`,
                                `It can be a difficult place for people to live in.`
                            ],
                            correctIndex: 2,
                            explanation: `The narrator explains that "when there's a high tide...then you know it's an island," indicating it's only completely isolated during high tides.`
                        },
                        {
                            questionNumber: 32,
                            question: `What does the narrator suggest about their father?`,
                            options: [
                                `His writing prevents him from doing things he wants to with his family.`,
                                `His initial reaction to his son's request is different from usual.`,
                                `His true feelings are easily hidden from his daughter.`,
                                `His son's arrival is one event he will take time off for.`
                            ],
                            correctIndex: 3,
                            explanation: `Despite Dad's usual reluctance to be disturbed or go anywhere, the narrator could tell "by the sparkle in his eyes that he was really looking forward to seeing Marcus."`
                        },
                        {
                            questionNumber: 33,
                            question: `The narrator emphasizes their feelings of discomfort because they`,
                            options: [
                                `are embarrassed that they don't understand what their brother is talking about.`,
                                `feel confused about why they can't relate to their brother any more.`,
                                `are upset by the unexpected change in their brother's behaviour.`,
                                `feel foolish that their brother's attention is so important to them.`
                            ],
                            correctIndex: 2,
                            explanation: `The narrator describes "that kind of discomfort you feel when someone you like...suddenly starts acting like a complete idiot," showing upset at the unexpected change.`
                        },
                        {
                            questionNumber: 34,
                            question: `In the fourth paragraph, what is the narrator's purpose in describing the island?`,
                            options: [
                                `to express their positive feelings about it`,
                                `to explain how the road was built`,
                                `to illustrate what kind of weather was usual`,
                                `to describe their journey home`
                            ],
                            correctIndex: 0,
                            explanation: `The narrator uses positive descriptive language like "welcomed us home," "beautifully hazy," and "wonderful silver light" to express positive feelings about the island.`
                        },
                        {
                            questionNumber: 35,
                            question: `The phrase 'this slower pace' refers to`,
                            options: [
                                `the way their father drives when approaching the island.`,
                                `the general rhythm of life on the island compared to elsewhere.`,
                                `the time it takes to cross from the mainland to the island.`,
                                `the way people walk along the footpaths on the island.`
                            ],
                            correctIndex: 1,
                            explanation: `The text states "The island had always been our sanctuary, a place where the pace of life was different," referring to the general rhythm of island life.`
                        },
                        {
                            questionNumber: 36,
                            question: `What do we learn about the narrator's reaction to the stranger?`,
                            options: [
                                `They felt his air of confidence contrasted with his physical appearance.`,
                                `They were able to come up with a reason for him being there.`,
                                `They realized their first impression of him was inaccurate.`,
                                `They found his calm demeanor contrasted with their brother's restlessness.`
                            ],
                            correctIndex: 3,
                            explanation: `The narrator contrasts Marcus who "seemed restless and full of nervous energy" with the stranger who "appeared calm and purposeful."`
                        }
                    ]
                },
                {
                    id: `text2_${randomSeed}`,
                    text: `The old lighthouse stood on the cliff edge, its white tower stark against the grey morning sky. Sarah had been coming here every day for three weeks now, ever since she'd moved to the coastal village to start her new job as a marine biologist studying ${topic}. The lighthouse keeper, an elderly man named Tom, had become her unofficial guide to the area's history and wildlife.

"You see that patch of water there?" Tom pointed towards a darker area of sea about half a mile from shore. "That's where the old merchant ship went down in 1847. Terrible storm it was, but the lighthouse saved most of the crew." Sarah nodded, making notes in her waterproof notebook. She was documenting the local ecosystem for her research on ${topic}, but found herself equally fascinated by the human stories connected to this place.

The lighthouse had been automated for twenty years now, but Tom still lived in the keeper's cottage, maintaining the building and grounds as a volunteer. "People think it's lonely up here," he said, "but I've got the seabirds for company, and researchers like you visiting all the time. Plus, there's something peaceful about watching the sea change with the weather and seasons."

Sarah understood what he meant. After years of working in busy university laboratories, the rhythm of coastal life was exactly what she needed. Her research on ${topic} required careful observation of marine patterns, and the lighthouse provided the perfect vantage point. She could see fishing boats heading out at dawn, pleasure craft returning in the evening, and the constant dance of seabirds diving for fish.

As they walked back towards the cottage, Tom showed her his collection of objects washed up by the tide over the years. "The sea gives up its secrets slowly," he said, handling a piece of sea glass worn smooth by decades of waves. "Just like your research, I imagine. You can't rush the ocean or the creatures that live in it."

That evening, as Sarah reviewed her notes by lamplight, she realized that Tom's words applied to more than just her scientific work. Moving to this remote place had been a leap of faith, leaving behind the security of her previous position for the uncertainty of independent research. But already, she could feel the benefits of this slower, more thoughtful approach to both work and life.`,

                    questions: [
                        {
                            questionNumber: 31,
                            question: `In the first paragraph, what is Sarah's main reason for visiting the lighthouse?`,
                            options: [
                                `She is researching the history of maritime disasters.`,
                                `She is conducting scientific research in the area.`,
                                `She is writing a book about lighthouse keepers.`,
                                `She is considering buying property in the village.`
                            ],
                            correctIndex: 1,
                            explanation: `Sarah is described as "a marine biologist studying ${topic}" who moved to the village "to start her new job."`
                        },
                        {
                            questionNumber: 32,
                            question: `What does Tom suggest about living at the lighthouse?`,
                            options: [
                                `It can be isolating but has compensations.`,
                                `It requires special training and qualifications.`,
                                `It is more demanding than people realize.`,
                                `It attracts too many visitors during summer.`
                            ],
                            correctIndex: 0,
                            explanation: `Tom acknowledges that "People think it's lonely up here" but explains he has "the seabirds for company" and finds it "peaceful."`
                        },
                        {
                            questionNumber: 33,
                            question: `Sarah's move to the coastal village represents`,
                            options: [
                                `a temporary assignment from her university.`,
                                `a significant change in her career approach.`,
                                `a return to her childhood home.`,
                                `a step backwards in her professional development.`
                            ],
                            correctIndex: 1,
                            explanation: `The text describes her leaving "the security of her previous position for the uncertainty of independent research," showing a major career change.`
                        },
                        {
                            questionNumber: 34,
                            question: `The phrase 'The sea gives up its secrets slowly' refers to`,
                            options: [
                                `the difficulty of finding objects on the beach.`,
                                `the need for patience in understanding natural processes.`,
                                `the challenge of predicting weather patterns.`,
                                `the time it takes for shipwrecks to be discovered.`
                            ],
                            correctIndex: 1,
                            explanation: `Tom connects this phrase to Sarah's research, saying "You can't rush the ocean or the creatures that live in it," emphasizing the need for patience in scientific observation.`
                        },
                        {
                            questionNumber: 35,
                            question: `What do we learn about Sarah's previous work environment?`,
                            options: [
                                `It was located near the coast.`,
                                `It was fast-paced and busy.`,
                                `It focused on lighthouse preservation.`,
                                `It involved working with Tom.`
                            ],
                            correctIndex: 1,
                            explanation: `The text mentions "busy university laboratories" as her previous work environment, contrasting with the peaceful coastal setting.`
                        },
                        {
                            questionNumber: 36,
                            question: `By the end of the passage, Sarah feels`,
                            options: [
                                `uncertain about her decision to move.`,
                                `eager to return to university life.`,
                                `confident about her new approach.`,
                                `concerned about her research progress.`
                            ],
                            correctIndex: 2,
                            explanation: `Sarah "could feel the benefits of this slower, more thoughtful approach to both work and life," showing confidence in her decision.`
                        }
                    ]
                },
                {
                    id: `text3_${randomSeed}`,
                    text: `The train pulled into the station exactly on time, as it had done every weekday for the past fifteen years. Emma gathered her belongings and joined the familiar crowd of commuters heading for the exit. But today was different – today was her last day working in the city, her last journey on this particular route. Tomorrow she would begin a new chapter of her life, working remotely from her cottage in the countryside, focusing on her passion for ${topic}.

The decision hadn't been easy. Emma had built a successful career in marketing, working for some of the city's most prestigious companies. Her colleagues thought she was crazy to give up the excitement and opportunities of urban life for what they saw as isolation and boredom. "You'll miss the energy of the city," her manager had warned. "And what about your career prospects?"

But Emma had different priorities now. The pandemic had shown her that many jobs could be done just as effectively from home, and she'd discovered a love for ${topic} during the long months of lockdown. What had started as a hobby had gradually become a serious interest, then a qualification, and finally a new career path. The cottage she'd inherited from her grandmother provided the perfect base for this new venture.

As she walked through the city streets for the last time as a commuter, Emma reflected on how much her perspective had changed. The noise and crowds that had once energized her now felt overwhelming. The constant pressure to be available, to respond immediately to emails and messages, had become exhausting rather than exciting. She longed for the quiet mornings in her garden, the time to think and plan without interruption.

The cottage was small but perfectly formed, with a study that looked out over rolling hills and a garden that was already showing signs of spring. Emma had spent weekends there for months, gradually preparing for this transition. She'd set up her home office, established reliable internet connections, and begun building relationships with local suppliers and potential clients interested in ${topic}.

Her grandmother would have approved, Emma thought. The old woman had always valued independence and self-sufficiency, growing her own vegetables and making her own preserves. "The secret to happiness," she used to say, "is knowing what you really need, not what other people think you should want." Emma was finally beginning to understand what she meant.`,

                    questions: [
                        {
                            questionNumber: 31,
                            question: `What is significant about Emma's train journey?`,
                            options: [
                                `It is her first time traveling to the countryside.`,
                                `It marks the end of her daily commuting routine.`,
                                `It is taking longer than usual due to delays.`,
                                `It is the first time she has traveled alone.`
                            ],
                            correctIndex: 1,
                            explanation: `The text states "today was her last day working in the city, her last journey on this particular route."`
                        },
                        {
                            questionNumber: 32,
                            question: `How do Emma's colleagues view her decision?`,
                            options: [
                                `They are envious of her new lifestyle.`,
                                `They think she is making a mistake.`,
                                `They are supportive of her career change.`,
                                `They plan to follow her example.`
                            ],
                            correctIndex: 1,
                            explanation: `Her colleagues "thought she was crazy" and her manager warned about missing city life and career prospects.`
                        },
                        {
                            questionNumber: 33,
                            question: `Emma's interest in her new field developed`,
                            options: [
                                `suddenly during a career crisis.`,
                                `through encouragement from her grandmother.`,
                                `gradually over an extended period.`,
                                `as a result of losing her city job.`
                            ],
                            correctIndex: 2,
                            explanation: `The text describes a progression: "What had started as a hobby had gradually become a serious interest, then a qualification, and finally a new career path."`
                        },
                        {
                            questionNumber: 34,
                            question: `The cottage represents for Emma`,
                            options: [
                                `a temporary escape from city pressures.`,
                                `an unwanted responsibility she has inherited.`,
                                `the foundation for her new way of life.`,
                                `a reminder of her grandmother's loneliness.`
                            ],
                            correctIndex: 2,
                            explanation: `The cottage "provided the perfect base for this new venture" and she had been "gradually preparing for this transition."`
                        },
                        {
                            questionNumber: 35,
                            question: `Emma's grandmother's philosophy emphasized`,
                            options: [
                                `the importance of financial security.`,
                                `the value of understanding your true needs.`,
                                `the benefits of living in the city.`,
                                `the necessity of following family traditions.`
                            ],
                            correctIndex: 1,
                            explanation: `Her grandmother said "The secret to happiness is knowing what you really need, not what other people think you should want."`
                        },
                        {
                            questionNumber: 36,
                            question: `By the end of the passage, Emma appears to be`,
                            options: [
                                `having second thoughts about her decision.`,
                                `worried about the practical challenges ahead.`,
                                `at peace with her choice and its implications.`,
                                `uncertain about her grandmother's approval.`
                            ],
                            correctIndex: 2,
                            explanation: `Emma is "finally beginning to understand" her grandmother's wisdom and has been thoughtfully preparing for her new life.`
                        }
                    ]
                }
            ];

            const textIndex = Math.floor(Math.random() * readingTexts.length);
            const selectedText = readingTexts[textIndex];

            return {
                generationId: requestId,
                partNumber: 5,
                topic: topic,
                title: `Extract from a novel`,
                instructions: "Read the extract from a novel below. For each question, choose the correct answer.",
                text: selectedText.text,
                questions: selectedText.questions,
                timestamp: timestamp,
                uniqueId: selectedText.id,
                isAIGenerated: false,
                isFallback: true,
                generationTime: Date.now() - timestamp
            };
        }

        function generatePart6Content(topic, requestId, timestamp) {
            // Cambridge B2 First Part 6: Gapped Text (6 questions, 37-42)
            // Newspaper article with 6 sentences removed, 7 options provided (1 extra)

            // Generate unique content each time
            const randomSeed = Math.random().toString(36).substr(2, 9);
            const timeStamp = new Date().toISOString();

            const gappedTexts = [
                {
                    id: `gapped1_${randomSeed}`,
                    title: `Good preparation leads to success in ${topic}`,
                    subtitle: `A former professional explains what ${topic} training actually involves.`,
                    text: `What we ${topic} professionals do is instinctive, but instinct learnt through a decade of training. A professional's life in this field is hard to understand, and easy to misinterpret. Many a journalist and commentator has tried to do so, but even they have chosen to interpret all the hard work and physical discipline as obsessive. And so the idea persists that professionals spend every waking hour in pain, bodies at breaking point, their smiles a pretence.

As a former professional in ${topic} here in Britain, I would beg to question this. [GAP 37] With expert teaching and daily practice, its various demands are easily within the capacity of the healthy human body. Contrary to popular belief, there is no need to break bones or tear muscles to achieve professional standards. It is simply a question of sufficient conditioning of the muscular system.

Over the course of my professional life I worked my way through at least 10,000 training sessions. I took my first at a school of ${topic} at the age of seven and my last 36 years later at the national training centre in London. In the years between, training was the first thing I did every day. It starts at an early age, this daily ritual, because it has to.

[GAP 38] But for a ${topic} professional in particular, this lengthy period has to come before the effects of adolescence set in, while maximum flexibility can still be achieved.

Those first sessions I took were remarkably similar to the last. In fact, taking into account the occasional new idea, ${topic} training methods have changed little since 1820, when the details of technique were first written down, and are easily recognised in any country. Starting with the left hand on a horizontal bar fixed at waist height, the trainee performs a series of slow, controlled movements, gradually warming up the muscles and preparing the body for the more complex movements that follow.

[GAP 39] The training session offers some support, but the real strength is in the muscles, built up through years of conditioning.

[GAP 40] As technology takes away activity from the lives of many, perhaps the ${topic} professional's physicality is ever more difficult for most people to imagine.

[GAP 41] No one avoids this: it is ${topic}'s great democratiser, the well established members of the profession working alongside the newest recruits.

[GAP 42] It takes at least a decade of high-quality, regular practice to become an expert in any physical discipline.`,

                    options: [
                        "A. Through endless tries at the usual exercises and frequent failures, ${topic} professionals develop the neural pathways in the brain necessary to control accurate, fast and smooth movement.",
                        "B. The training equipment offers some support, but the real strength is in the muscles, built up through training.",
                        "C. As technology takes away activity from the lives of many, perhaps the ${topic} professional's physicality is ever more difficult for most people to imagine.",
                        "D. ${topic} technique is certainly extreme but it is not, in itself, dangerous.",
                        "E. The principle is identical in the gym – pushing yourself to the limit, but not beyond, will eventually bring the desired result.",
                        "F. No one avoids this: it is ${topic}'s great democratiser, the well established members of the profession working alongside the newest recruits.",
                        "G. It takes at least a decade of high-quality, regular practice to become an expert in any physical discipline."
                    ],

                    correctAnswers: [3, 6, 1, 2, 5, 0], // D, G, B, C, F, A (0-indexed)
                    explanations: [
                        "Gap 37: Option D fits because it directly responds to the misconception mentioned in the previous paragraph about the field being dangerous.",
                        "Gap 38: Option G fits because it explains why training must start early, connecting to the idea of a 'lengthy period'.",
                        "Gap 39: Option B fits because it follows the description of training equipment and muscle development.",
                        "Gap 40: Option C fits because it provides context about modern society and physical activity.",
                        "Gap 41: Option F fits because it emphasizes the universal nature of the training requirements.",
                        "Gap 42: Option A fits because it explains how the training process develops the necessary neural pathways."
                    ]
                },
                {
                    id: `gapped2_${randomSeed}`,
                    title: `Breaking barriers in ${topic}`,
                    subtitle: `Dr. Sarah Chen discusses the challenges and opportunities in modern ${topic} research.`,
                    text: `The field of ${topic} has undergone remarkable transformation in recent years. What once seemed impossible is now becoming routine practice in laboratories around the world. However, this progress has not come without significant challenges and ongoing debates about the best approaches to take.

[GAP 37] Many researchers argue that traditional methods are no longer sufficient to address the complex problems we face today. The integration of new technologies and methodologies has opened up possibilities that were unimaginable just a decade ago.

Dr. Chen, who has been working in ${topic} for over fifteen years, has witnessed this evolution firsthand. "When I started my career, we were limited by both technology and understanding," she explains. "Now, we have tools and knowledge that allow us to tackle problems from multiple angles simultaneously."

[GAP 38] This collaborative approach has led to breakthrough discoveries that have practical applications in everyday life. The research community has learned that sharing knowledge and resources accelerates progress for everyone involved.

The current generation of researchers brings fresh perspectives to age-old questions. [GAP 39] Their willingness to challenge established thinking has resulted in innovative solutions that combine traditional wisdom with cutting-edge technology.

[GAP 40] However, this rapid pace of change also presents challenges. Keeping up with new developments requires continuous learning and adaptation, which can be demanding for both researchers and institutions.

[GAP 41] The future of ${topic} depends on maintaining this balance between innovation and practical application. Success will require not only technical expertise but also the ability to communicate complex ideas to diverse audiences.

[GAP 42] As Dr. Chen concludes, "The most exciting discoveries often come from unexpected connections between different areas of knowledge."`,

                    options: [
                        "A. The most exciting discoveries often come from unexpected connections between different areas of knowledge.",
                        "B. Young researchers are not afraid to question conventional approaches and explore new possibilities.",
                        "C. The future of the field depends on maintaining this balance between innovation and practical application.",
                        "D. Traditional methods alone are no longer sufficient to address the complex problems we face today.",
                        "E. This collaborative approach has led to breakthrough discoveries with practical applications.",
                        "F. However, this rapid pace of change also presents significant challenges for everyone involved.",
                        "G. Keeping up with new developments requires continuous learning and adaptation from all participants."
                    ],

                    correctAnswers: [3, 4, 1, 5, 2, 0], // D, E, B, F, C, A (0-indexed)
                    explanations: [
                        "Gap 37: Option D fits because it follows the discussion about traditional methods being insufficient.",
                        "Gap 38: Option E fits because it explains the results of the collaborative approach mentioned.",
                        "Gap 39: Option B fits because it describes the characteristics of the current generation of researchers.",
                        "Gap 40: Option F fits because it introduces the challenges that follow the rapid pace of change.",
                        "Gap 41: Option C fits because it discusses the future requirements for the field.",
                        "Gap 42: Option A fits because it provides Dr. Chen's concluding statement about discoveries."
                    ]
                }
            ];

            const textIndex = Math.floor(Math.random() * gappedTexts.length);
            const selectedText = gappedTexts[textIndex];

            return {
                generationId: requestId,
                partNumber: 6,
                topic: topic,
                title: selectedText.title,
                subtitle: selectedText.subtitle,
                instructions: "Read the newspaper article below. Six sentences have been removed from the text below. For each question, choose the correct answer. There is one extra sentence which you do not need to use.",
                text: selectedText.text,
                options: selectedText.options,
                correctAnswers: selectedText.correctAnswers,
                explanations: selectedText.explanations,
                timestamp: timestamp,
                uniqueId: selectedText.id,
                isAIGenerated: false,
                isFallback: true,
                generationTime: Date.now() - timestamp
            };
        }

        function generatePart7Content(topic, requestId, timestamp) {
            // Cambridge B2 First Part 7: Multiple Matching (10 questions, 43-52)
            // Newspaper article with labeled paragraphs, match information to sections

            // Generate unique content each time
            const randomSeed = Math.random().toString(36).substr(2, 9);
            const timeStamp = new Date().toISOString();

            const multipleMatchingTexts = [
                {
                    id: `matching1_${randomSeed}`,
                    title: `Rising Star`,
                    subtitle: `Margaret Garelly goes to meet Duncan Williams, who works in ${topic}.`,

                    paragraphs: [
                        {
                            letter: "A",
                            text: `It's my first time driving to the ${topic} training centre and I turn off slightly too early at the university playing fields. Had he accepted the rejections in his early teenage years, it is exactly the sort of place Duncan Williams would have found himself working at weekends. At his current age of 18, he would have been a bright first-year undergraduate mixing his academic studies with a bit of sport and research, given his early talent in all these areas. However, Duncan undoubtedly took the right path. Instead of studying, he is sitting with his father Gavin in one of the interview rooms at the professional training base reflecting on Saturday's breakthrough in ${topic}. Such has been his rise to fame that it is with some disbelief that you listen to him describing how his career was nearly all over before it began.`
                        },
                        {
                            letter: "B",
                            text: `Gavin, himself a fine professional – a member of the national team in his time – and now a professional coach, sent Duncan to three professional organizations as a 14-year-old, but all three turned him down. 'I worked with him a lot when he was around 12, and it was clear he had fantastic technique and skill. But then the other boys shot up in height and he didn't. But I was still upset and surprised that no team seemed to want him, that they couldn't see what he might develop into in time. When the current organization accepted him as a junior, it was made clear to him that this was more of a last chance than a new beginning. They told him he had a lot of hard work to do and wasn't part of their plans. Fortunately, that summer he just grew and grew, and got much stronger as well.'`
                        },
                        {
                            letter: "C",
                            text: `Duncan takes up the story: 'The first half of that season I worked in the junior program. I remember thinking that I was never going to make it to the senior level. I was still quite small and the other trainees seemed so much more advanced than me. I felt completely out of my depth and wondered if I should just give up and focus on my studies instead. But my father kept encouraging me to stick with it, and the coaches were very patient. They kept telling me that physical development comes at different times for different people, and that technique and dedication were more important than size at that stage.'`
                        },
                        {
                            letter: "D",
                            text: `'Then everything changed in my second year. I had grown about six inches over the summer and suddenly I could compete physically with the others. But more importantly, all that technical work I had done when I was smaller really paid off. While the other trainees were still learning basic skills, I was already quite advanced. The coaches started to take notice and I was moved up to train with the senior group. That's when I realized that all those difficult early years had actually been preparing me for this moment. Sometimes I think that if I had been bigger earlier, I might not have developed the same level of technical skill.'`
                        }
                    ],

                    questions: [
                        {
                            questionNumber: 43,
                            question: "states how surprised the writer was at Duncan's early difficulties?",
                            correctAnswer: "A",
                            explanation: "Paragraph A mentions 'it is with some disbelief that you listen to him describing how his career was nearly all over before it began.'"
                        },
                        {
                            questionNumber: 44,
                            question: "says that Duncan sometimes seems much more mature than he really is?",
                            correctAnswer: "D",
                            explanation: "Paragraph D shows Duncan's mature reflection: 'Sometimes I think that if I had been bigger earlier, I might not have developed the same level of technical skill.'"
                        },
                        {
                            questionNumber: 45,
                            question: "describes the frustration felt by Duncan's father?",
                            correctAnswer: "B",
                            explanation: "Paragraph B shows Gavin's frustration: 'But I was still upset and surprised that no team seemed to want him.'"
                        },
                        {
                            questionNumber: 46,
                            question: "mentions Duncan's thoughts about giving up?",
                            correctAnswer: "C",
                            explanation: "Paragraph C states 'I felt completely out of my depth and wondered if I should just give up and focus on my studies instead.'"
                        },
                        {
                            questionNumber: 47,
                            question: "explains how Duncan's father provided support?",
                            correctAnswer: "C",
                            explanation: "Paragraph C mentions 'But my father kept encouraging me to stick with it.'"
                        },
                        {
                            questionNumber: 48,
                            question: "describes a turning point in Duncan's development?",
                            correctAnswer: "D",
                            explanation: "Paragraph D explains 'Then everything changed in my second year' and describes his physical and technical development."
                        },
                        {
                            questionNumber: 49,
                            question: "suggests that Duncan's early setbacks were actually beneficial?",
                            correctAnswer: "D",
                            explanation: "Paragraph D reflects 'all those difficult early years had actually been preparing me for this moment.'"
                        },
                        {
                            questionNumber: 50,
                            question: "mentions the importance of patience in development?",
                            correctAnswer: "C",
                            explanation: "Paragraph C states 'the coaches were very patient' and explains their advice about development timing."
                        },
                        {
                            questionNumber: 51,
                            question: "indicates that Duncan's technical skills gave him an advantage?",
                            correctAnswer: "D",
                            explanation: "Paragraph D explains 'While the other trainees were still learning basic skills, I was already quite advanced.'"
                        },
                        {
                            questionNumber: 52,
                            question: "shows that Duncan's career path was unconventional?",
                            correctAnswer: "A",
                            explanation: "Paragraph A contrasts Duncan's path with the conventional university route he could have taken."
                        }
                    ]
                },
                {
                    id: `matching2_${randomSeed}`,
                    title: `Making a Difference`,
                    subtitle: `James Parker meets Dr. Lisa Rodriguez, a pioneer in ${topic} research.`,

                    paragraphs: [
                        {
                            letter: "A",
                            text: `It's my first time visiting Dr. Rodriguez's research facility and I arrive slightly early at the modern science complex. Had she followed the traditional academic path suggested during her undergraduate years, it is exactly the sort of corporate laboratory where many graduates would have found themselves working. At her current age of 35, she would have been a senior researcher managing routine projects and supervising junior staff, given her early achievements in ${topic}. However, Dr. Rodriguez undoubtedly took the right path. Instead of accepting a conventional position, she is sitting in her independent laboratory discussing the latest breakthrough in ${topic} research. Such has been her rise to international recognition that it is with some amazement that you listen to her describing how her career was nearly derailed before it truly began.`
                        },
                        {
                            letter: "B",
                            text: `Professor Williams, himself a distinguished academic – a recipient of several prestigious awards in his time – and now Dr. Rodriguez's former supervisor, recalls the early days when she first applied for graduate funding. 'I worked with Lisa extensively during her master's program, and it was clear she had exceptional analytical skills and innovative thinking. But then other candidates seemed to have more conventional research proposals and established connections. I was genuinely concerned that the funding committee might not recognize her unique potential, that they couldn't see what this individual might develop into with proper support. When our department finally accepted her proposal, it was made clear that this was more of an experimental opportunity than a guaranteed path. The committee emphasized that there would be significant obstacles ahead and that she wasn't initially considered for the main research program. Fortunately, that first year she demonstrated remarkable creativity and determination, exceeding all expectations.'`
                        },
                        {
                            letter: "C",
                            text: `Dr. Rodriguez takes up the story: 'The first six months of that research program I worked primarily on literature review and basic experiments. I remember feeling overwhelmed by the complexity of the field and questioning whether I had chosen the right direction. The other graduate students seemed so confident and knowledgeable, while I was struggling to understand even the fundamental concepts. There were moments when I seriously considered switching to a more traditional research area. But Professor Williams was incredibly supportive, always encouraging me to persist and helping me see the broader implications of my work. He would often remind me that every breakthrough starts with someone willing to ask different questions, and that the most important quality in research is curiosity rather than existing expertise.'`
                        },
                        {
                            letter: "D",
                            text: `'The turning point came during my second year in the program. I was working on what seemed like a routine data analysis when I noticed some unexpected patterns in the results. Most researchers would have dismissed it as experimental error, but something made me investigate further. Professor Williams encouraged me to pursue this line of inquiry, even though it meant deviating from my original research plan. What we discovered completely changed our understanding of certain aspects of ${topic}. The results were so significant that we were invited to present our findings at the International Conference on ${topic} Innovation. Standing on that stage, presenting to hundreds of experts from around the world, I realized that I had found my true calling and that taking risks in research can lead to extraordinary discoveries.'`
                        }
                    ],

                    questions: [
                        {
                            questionNumber: 43,
                            question: "states how surprised the writer was at Dr. Rodriguez's early struggles?",
                            correctAnswer: "A",
                            explanation: "Paragraph A mentions 'it is with some amazement that you listen to her describing how her career was nearly derailed before it truly began.'"
                        },
                        {
                            questionNumber: 44,
                            question: "says that Dr. Rodriguez sometimes demonstrates wisdom beyond her experience?",
                            correctAnswer: "D",
                            explanation: "Paragraph D shows Dr. Rodriguez's mature insight: 'taking risks in research can lead to extraordinary discoveries.'"
                        },
                        {
                            questionNumber: 45,
                            question: "describes the concern felt by Dr. Rodriguez's supervisor?",
                            correctAnswer: "B",
                            explanation: "Paragraph B shows Professor Williams's worry: 'I was genuinely concerned that the funding committee might not recognize her unique potential.'"
                        },
                        {
                            questionNumber: 46,
                            question: "mentions Dr. Rodriguez's thoughts about changing direction?",
                            correctAnswer: "C",
                            explanation: "Paragraph C states 'There were moments when I seriously considered switching to a more traditional research area.'"
                        },
                        {
                            questionNumber: 47,
                            question: "explains how Dr. Rodriguez's supervisor provided guidance?",
                            correctAnswer: "C",
                            explanation: "Paragraph C mentions 'Professor Williams was incredibly supportive, always encouraging me to persist and helping me see the broader implications.'"
                        },
                        {
                            questionNumber: 48,
                            question: "describes a moment of professional achievement?",
                            correctAnswer: "D",
                            explanation: "Paragraph D mentions being 'invited to present our findings at the International Conference' and 'presenting to hundreds of experts.'"
                        },
                        {
                            questionNumber: 49,
                            question: "suggests that Dr. Rodriguez's unconventional approach was ultimately beneficial?",
                            correctAnswer: "D",
                            explanation: "Paragraph D reflects 'What we discovered completely changed our understanding' and describes the significant impact of her different approach."
                        },
                        {
                            questionNumber: 50,
                            question: "mentions the importance of curiosity in research?",
                            correctAnswer: "C",
                            explanation: "Paragraph C quotes Professor Williams emphasizing that 'the most important quality in research is curiosity rather than existing expertise.'"
                        },
                        {
                            questionNumber: 51,
                            question: "indicates that Dr. Rodriguez's discovery came from careful observation?",
                            correctAnswer: "D",
                            explanation: "Paragraph D explains how she 'noticed some unexpected patterns in the results' during routine analysis."
                        },
                        {
                            questionNumber: 52,
                            question: "shows that Dr. Rodriguez's career path was non-traditional?",
                            correctAnswer: "A",
                            explanation: "Paragraph A contrasts Dr. Rodriguez's choice with 'the traditional academic path suggested during her undergraduate years.'"
                        }
                    ]
                }
            ];

            const textIndex = Math.floor(Math.random() * multipleMatchingTexts.length);
            const selectedText = multipleMatchingTexts[textIndex];

            return {
                generationId: requestId,
                partNumber: 7,
                topic: topic,
                title: selectedText.title,
                subtitle: selectedText.subtitle,
                instructions: "Read the newspaper article below about a professional. For each question, choose the correct answer. Each answer may be chosen more than once.",
                paragraphs: selectedText.paragraphs,
                questions: selectedText.questions,
                timestamp: timestamp,
                uniqueId: selectedText.id,
                isAIGenerated: false,
                isFallback: true,
                generationTime: Date.now() - timestamp
            };
        }

        function generateAdvancedWritingContent(topic, requestId) {
            // Cambridge B2 First Writing Part 1: Essay (140-190 words)
            // Must include: introduction to topic, 3 bullet points (2 given + 1 own idea), personal opinion
            const writingPrompts = [
                {
                    context: `In your English class you have been talking about ${topic}. Now, your English teacher has asked you to write an essay.`,
                    essayPrompt: `Every country in the world has issues related to ${topic}. Do you think these problems can be solved?`,
                    bulletPoints: [
                        `government action and policies`,
                        `individual responsibility and choices`,
                        `........................... (your own idea)`
                    ]
                },
                {
                    context: `In your English class you have been talking about modern society. Now, your English teacher has asked you to write an essay.`,
                    essayPrompt: `Some people believe that ${topic} will have a positive impact on society, while others think it may cause more problems than benefits. What is your opinion?`,
                    bulletPoints: [
                        `potential benefits and opportunities`,
                        `possible risks and challenges`,
                        `........................... (your own idea)`
                    ]
                },
                {
                    context: `In your English class you have been talking about education and careers. Now, your English teacher has asked you to write an essay.`,
                    essayPrompt: `In your opinion, how important is knowledge about ${topic} for young people today? Give reasons for your point of view.`,
                    bulletPoints: [
                        `current relevance in daily life`,
                        `future career opportunities`,
                        `........................... (your own idea)`
                    ]
                },
                {
                    context: `In your English class you have been talking about priorities in modern society. Now, your English teacher has asked you to write an essay.`,
                    essayPrompt: `Many experts argue that ${topic} should be a top priority for governments and organizations. Do you agree or disagree with this statement?`,
                    bulletPoints: [
                        `government investment and policies`,
                        `private sector involvement`,
                        `........................... (your own idea)`
                    ]
                }
            ];

            const promptIndex = Math.floor(Math.random() * writingPrompts.length);
            const selectedPrompt = writingPrompts[promptIndex];

            return {
                generationId: requestId,
                topic: topic,
                context: selectedPrompt.context,
                essayPrompt: selectedPrompt.essayPrompt,
                bulletPoints: selectedPrompt.bulletPoints,
                instructions: "Write an essay using all the notes and giving reasons for your point of view.",
                wordCount: "Write 140–190 words in an appropriate style.",
                timestamp: Date.now()
            };
        }

        async function generateReadingContent(partNumber = 1) {
            // Expanded topic pool with more variety
            const topics = [
                'artificial intelligence and society',
                'climate change and environmental protection',
                'social media and digital communication',
                'sustainable tourism and travel',
                'remote work and modern lifestyle',
                'healthy eating and nutrition',
                'urban planning and smart cities',
                'renewable energy and technology',
                'space exploration and technology',
                'online education and learning',
                'sustainable fashion industry',
                'mental health awareness',
                'cryptocurrency and digital economy',
                'virtual reality applications',
                'food waste and sustainability',
                'cultural preservation in digital age',
                'biodiversity conservation efforts',
                'smart home technology trends',
                'electric vehicle adoption',
                'digital nomad lifestyle',
                'microplastic pollution solutions',
                'gene therapy breakthroughs',
                'vertical farming innovations',
                'quantum computing applications',
                'ocean cleanup technologies',
                'personalized medicine advances',
                'circular economy principles',
                'autonomous vehicle safety',
                'renewable energy storage',
                'digital wellness practices'
            ];

            // Enhanced randomization
            const shuffledTopics = [...topics].sort(() => Math.random() - 0.5);
            const randomIndex = Math.floor(Math.random() * shuffledTopics.length);
            const selectedTopic = shuffledTopics[randomIndex];

            const timestamp = Date.now();
            const randomSeed = Math.random().toString(36).substr(2, 9);
            const uniqueId = `${generationCounter}_${timestamp}_${randomSeed}`;

            console.log(`🎯 Generating Reading content #${generationCounter}`);
            console.log(`📚 Selected topic: ${selectedTopic}`);
            console.log(`🔢 Unique ID: ${uniqueId}`);
            console.log(`⏰ Timestamp: ${timestamp}`);

            const prompt = generatePartPrompt(partNumber, selectedTopic, uniqueId, timestamp, randomSeed);

            return await callGeminiAPI(prompt, 'reading');
        }

        function generatePartPrompt(partNumber, selectedTopic, uniqueId, timestamp, randomSeed) {
            const baseRequirements = `
🚨 CRITICAL UNIQUENESS REQUIREMENTS:
- This is generation #${generationCounter} in session ${sessionId}
- Unique ID: ${uniqueId}
- Topic: ${selectedTopic}
- Timestamp: ${timestamp}
- Random seed: ${randomSeed}

⚠️ MANDATORY: This content must be COMPLETELY DIFFERENT from any previous generation.`;

            switch(partNumber) {
                case 1:
                    return `You are a Cambridge B2 First exam creator. Create a COMPLETELY NEW Part 1: Multiple Choice Cloze about "${selectedTopic}".
${baseRequirements}

📝 PART 1 SPECIFICATIONS:
- Create a 180-220 word text about ${selectedTopic}
- Include 8 gaps testing: articles, prepositions, conjunctions, vocabulary, phrasal verbs, collocations
- Each gap has 4 realistic options with only 1 clearly correct answer

📋 OUTPUT FORMAT (JSON ONLY):
{
  "generationId": "${uniqueId}",
  "partNumber": 1,
  "topic": "${selectedTopic}",
  "title": "Creative title about ${selectedTopic}",
  "text": "Text with [GAP1], [GAP2], [GAP3], [GAP4], [GAP5], [GAP6], [GAP7], [GAP8]",
  "questions": [
    {"gapNumber": 1, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 0, "explanation": "Why correct"},
    {"gapNumber": 2, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 1, "explanation": "Why correct"},
    {"gapNumber": 3, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 2, "explanation": "Why correct"},
    {"gapNumber": 4, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 3, "explanation": "Why correct"},
    {"gapNumber": 5, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 0, "explanation": "Why correct"},
    {"gapNumber": 6, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 1, "explanation": "Why correct"},
    {"gapNumber": 7, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 2, "explanation": "Why correct"},
    {"gapNumber": 8, "options": ["opt1", "opt2", "opt3", "opt4"], "correctIndex": 3, "explanation": "Why correct"}
  ]
}`;

                case 2:
                    return `You are a Cambridge B2 First exam creator. Create a COMPLETELY NEW Part 2: Open Cloze about "${selectedTopic}".
${baseRequirements}

📝 PART 2 SPECIFICATIONS:
- Create a 150-180 word text about ${selectedTopic}
- Include 8 gaps where students must provide the missing word (no options given)
- Focus on: articles, prepositions, pronouns, auxiliary verbs, conjunctions, relative pronouns

📋 OUTPUT FORMAT (JSON ONLY):
{
  "generationId": "${uniqueId}",
  "partNumber": 2,
  "topic": "${selectedTopic}",
  "title": "Creative title about ${selectedTopic}",
  "text": "Text with [GAP1], [GAP2], [GAP3], [GAP4], [GAP5], [GAP6], [GAP7], [GAP8]",
  "answers": ["word1", "word2", "word3", "word4", "word5", "word6", "word7", "word8"],
  "explanations": ["Why word1", "Why word2", "Why word3", "Why word4", "Why word5", "Why word6", "Why word7", "Why word8"]
}`;

                case 3:
                    return `You are a Cambridge B2 First exam creator. Create a COMPLETELY NEW Part 3: Word Formation about "${selectedTopic}".
${baseRequirements}

📝 PART 3 SPECIFICATIONS:
- Create a 150-180 word text about ${selectedTopic}
- Include 8 gaps where students transform given base words
- Focus on: prefixes, suffixes, word families, parts of speech changes

📋 OUTPUT FORMAT (JSON ONLY):
{
  "generationId": "${uniqueId}",
  "partNumber": 3,
  "topic": "${selectedTopic}",
  "title": "Creative title about ${selectedTopic}",
  "text": "Text with [GAP1], [GAP2], [GAP3], [GAP4], [GAP5], [GAP6], [GAP7], [GAP8]",
  "questions": [
    {"gapNumber": 1, "baseWord": "MANAGE", "correctAnswer": "management", "explanation": "Why this form"},
    {"gapNumber": 2, "baseWord": "SUCCESS", "correctAnswer": "successful", "explanation": "Why this form"},
    {"gapNumber": 3, "baseWord": "DEVELOP", "correctAnswer": "development", "explanation": "Why this form"},
    {"gapNumber": 4, "baseWord": "EFFECT", "correctAnswer": "effective", "explanation": "Why this form"},
    {"gapNumber": 5, "baseWord": "NATURE", "correctAnswer": "natural", "explanation": "Why this form"},
    {"gapNumber": 6, "baseWord": "IMPORTANT", "correctAnswer": "importance", "explanation": "Why this form"},
    {"gapNumber": 7, "baseWord": "POSSIBLE", "correctAnswer": "possibility", "explanation": "Why this form"},
    {"gapNumber": 8, "baseWord": "SCIENCE", "correctAnswer": "scientific", "explanation": "Why this form"}
  ]
}`;

                default:
                    return `You are a Cambridge B2 First exam creator. Create a COMPLETELY NEW Part ${partNumber} exercise about "${selectedTopic}".
${baseRequirements}

📝 SPECIFICATIONS:
- Create appropriate content for Part ${partNumber}
- Follow B2 First exam format and difficulty
- Include proper questions and answers

📋 OUTPUT FORMAT (JSON ONLY):
{
  "generationId": "${uniqueId}",
  "partNumber": ${partNumber},
  "topic": "${selectedTopic}",
  "title": "Creative title about ${selectedTopic}",
  "content": "Appropriate content for Part ${partNumber}"
}`;
            }
        }

        async function generateWritingContent() {
            // Expanded writing topics with more variety
            const topics = [
                'technology and happiness',
                'social media and real relationships',
                'environmental responsibility',
                'work-life balance',
                'cultural exchange and travel',
                'education and future careers',
                'healthy lifestyle choices',
                'urban vs rural living',
                'artificial intelligence in daily life',
                'sustainable transportation',
                'digital privacy concerns',
                'remote work culture',
                'fast fashion impact',
                'food security issues',
                'renewable energy adoption',
                'mental health in modern society',
                'digital detox benefits',
                'space tourism ethics',
                'genetic modification debates',
                'virtual reality education',
                'cryptocurrency environmental impact',
                'aging population challenges',
                'cultural diversity preservation',
                'smart city development',
                'ocean conservation efforts',
                'alternative protein sources',
                'digital art authenticity',
                'autonomous vehicle ethics',
                'climate change adaptation',
                'social entrepreneurship impact'
            ];

            // Enhanced randomization for writing topics
            const shuffledTopics = [...topics].sort(() => Math.random() - 0.5);
            const randomIndex = Math.floor(Math.random() * shuffledTopics.length);
            const selectedTopic = shuffledTopics[randomIndex];

            const timestamp = Date.now();
            const randomSeed = Math.random().toString(36).substr(2, 9);
            const uniqueId = `${generationCounter}_${timestamp}_${randomSeed}`;

            console.log(`✍️ Generating Writing content #${generationCounter}`);
            console.log(`📝 Selected topic: ${selectedTopic}`);
            console.log(`🔢 Unique ID: ${uniqueId}`);
            console.log(`⏰ Timestamp: ${timestamp}`);

            const prompt = `
You are a Cambridge B2 First exam creator. Create a COMPLETELY NEW and UNIQUE Writing Part 1 essay task about "${selectedTopic}".

🚨 CRITICAL UNIQUENESS REQUIREMENTS:
- This is generation #${generationCounter} in session ${sessionId}
- Unique ID: ${uniqueId}
- Topic: ${selectedTopic}
- Timestamp: ${timestamp}
- Random seed: ${randomSeed}

⚠️ MANDATORY: This essay task must be COMPLETELY DIFFERENT from any previous generation. Use the unique identifiers above to ensure originality.

📝 CONTENT SPECIFICATIONS:
- Create an engaging, thought-provoking essay question about ${selectedTopic}
- Question must require students to express opinions and give reasons
- Must be relevant to B2 students (age 16-25)
- Should encourage critical thinking and personal reflection
- Include 3 bullet points to guide essay structure

🎯 TOPIC FOCUS: ${selectedTopic}
Make this essay question specifically about ${selectedTopic} with fresh perspectives, current relevance, or unique angles.

📋 OUTPUT FORMAT (JSON ONLY):
{
  "generationId": "${uniqueId}",
  "topic": "${selectedTopic}",
  "essayPrompt": "Engaging and specific question or statement about ${selectedTopic} that requires discussion and personal opinion",
  "bulletPoints": [
    "First specific aspect to discuss related to ${selectedTopic}",
    "Second specific aspect to discuss related to ${selectedTopic}",
    "........................... (your own idea)"
  ]
}

🔥 GENERATE FRESH, ORIGINAL ESSAY TASK NOW!`;

            return await callGeminiAPI(prompt, 'writing');
        }

        // Generate and show functions using the new intelligent content generation system
        async function generateAndShowReading(partNumber = 1) {
            const startTime = Date.now();
            console.log(`🚀 Starting Reading Part ${partNumber} generation at ${startTime}`);

            showLoadingScreen(`🤖 Generating Reading Part ${partNumber}...`, `Intelligent AI system is creating new Part ${partNumber} questions just for you!`);

            try {
                // Use the new intelligent content generation system
                const requestId = `reading_p${partNumber}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                generatedContent = await generateIntelligentContent('reading', partNumber, null, requestId);

                // Add generation metadata
                generatedContent.isAIGenerated = true;
                generatedContent.generationTime = Date.now() - startTime;
                generatedContent.partNumber = partNumber;

                console.log('✅ Successfully generated intelligent content:', generatedContent);
                console.log(`⏱️ Generation took ${generatedContent.generationTime}ms`);

                showGeneratedReading();

            } catch (error) {
                console.error('❌ Failed to generate content:', error);
                console.log('🔄 Falling back to direct content generation...');
                generateFallbackReading(partNumber);
            }
        }

        function generateFallbackReading(partNumber = 1) {
            console.log(`🔧 Generating fallback content for Part ${partNumber} using intelligent system`);

            try {
                // Use the intelligent content generation system as fallback
                const requestId = `fallback_p${partNumber}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                const content = generateAdvancedReadingContent(partNumber, null, requestId);

                // Set the global variable for display
                generatedContent = {
                    ...content,
                    isAIGenerated: false,
                    isFallback: true,
                    generationTime: 0
                };

                console.log(`✅ Generated fallback Part ${partNumber} content:`, generatedContent);
                showGeneratedReading();
            } catch (error) {
                console.error('❌ Fallback generation failed:', error);
                // Show error message to user
                document.getElementById('modalTitle').textContent = 'Generation Error';
                document.getElementById('modalContent').innerHTML = `
                    <div class="text-center p-8">
                        <div class="text-red-500 text-6xl mb-4">❌</div>
                        <h3 class="text-xl font-bold text-red-600 mb-4">Content Generation Failed</h3>
                        <p class="text-gray-600 mb-6">Unable to generate Part ${partNumber} content. Please try again.</p>
                        <button onclick="closeModal()" class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                            Close
                        </button>
                    </div>
                `;
                openModal();
            }
        }

        function generateFallbackText(topic) {
            const templates = [
                `The impact of ${topic} on modern society cannot be overstated. Recent developments have shown that [GAP1] this field continues to evolve rapidly. Experts [GAP2] that we are only beginning to understand the full implications. [GAP3] the challenges are significant, the potential benefits are enormous. Many organizations are [GAP4] investing heavily in research and development. The future [GAP5] on our ability to adapt and innovate. [GAP6] we must consider the ethical implications carefully. [GAP7] collaboration between different sectors will be essential. [GAP8] the coming years will be crucial for determining our path forward.`,

                `Understanding ${topic} requires a comprehensive approach that considers multiple perspectives. [GAP1] the complexity of the issue, researchers have made significant progress. [GAP2] studies indicate that public awareness is growing. The [GAP3] between theory and practice remains a challenge. [GAP4] stakeholders must work together to find solutions. [GAP5] the importance of education cannot be ignored. [GAP6] we need to develop new strategies and methodologies. The [GAP7] of technology plays a crucial role in this process. [GAP8] success will depend on our collective efforts.`,

                `The evolution of ${topic} has transformed how we think about the future. [GAP1] traditional approaches are being questioned and revised. [GAP2] innovations are emerging at an unprecedented pace. [GAP3] the potential risks, the opportunities are compelling. [GAP4] leaders are calling for more sustainable practices. The [GAP5] between different generations is becoming more apparent. [GAP6] we must balance progress with responsibility. [GAP7] the global community is working towards common goals. [GAP8] the next decade will be transformative.`
            ];

            return templates[Math.floor(Math.random() * templates.length)];
        }

        function generateFallbackQuestions() {
            const questionSets = [
                [
                    { gapNumber: 1, options: ["Despite", "Although", "Because", "Since"], correctIndex: 0, explanation: "Despite introduces a contrast between the complexity and the progress made." },
                    { gapNumber: 2, options: ["Recent", "Previous", "Future", "Current"], correctIndex: 0, explanation: "Recent indicates that the studies are contemporary and relevant." },
                    { gapNumber: 3, options: ["gap", "link", "connection", "relationship"], correctIndex: 0, explanation: "Gap indicates a space or difference between theory and practice." },
                    { gapNumber: 4, options: ["All", "Some", "Many", "Few"], correctIndex: 0, explanation: "All emphasizes the comprehensive involvement of stakeholders." },
                    { gapNumber: 5, options: ["However", "Therefore", "Moreover", "Nevertheless"], correctIndex: 2, explanation: "Moreover adds additional information about education's importance." },
                    { gapNumber: 6, options: ["Clearly", "Obviously", "Certainly", "Definitely"], correctIndex: 0, explanation: "Clearly indicates that the need for new strategies is evident." },
                    { gapNumber: 7, options: ["role", "part", "function", "position"], correctIndex: 0, explanation: "Role is the most appropriate word to describe technology's function." },
                    { gapNumber: 8, options: ["Ultimate", "Final", "Complete", "Total"], correctIndex: 0, explanation: "Ultimate suggests the most important or final success." }
                ],
                [
                    { gapNumber: 1, options: ["in", "on", "at", "by"], correctIndex: 0, explanation: "In is the correct preposition to use with 'impact'." },
                    { gapNumber: 2, options: ["believe", "think", "consider", "suggest"], correctIndex: 0, explanation: "Believe is the most appropriate verb for expressing expert opinion." },
                    { gapNumber: 3, options: ["While", "When", "Where", "Whether"], correctIndex: 0, explanation: "While introduces a contrast between challenges and benefits." },
                    { gapNumber: 4, options: ["currently", "recently", "previously", "constantly"], correctIndex: 0, explanation: "Currently indicates present-time investment activities." },
                    { gapNumber: 5, options: ["depends", "relies", "rests", "hangs"], correctIndex: 0, explanation: "Depends is the most natural collocation with 'future'." },
                    { gapNumber: 6, options: ["However", "Therefore", "Moreover", "Furthermore"], correctIndex: 0, explanation: "However introduces a contrasting point about ethical considerations." },
                    { gapNumber: 7, options: ["Effective", "Successful", "Productive", "Efficient"], correctIndex: 0, explanation: "Effective collaboration is the most appropriate collocation." },
                    { gapNumber: 8, options: ["Undoubtedly", "Certainly", "Definitely", "Surely"], correctIndex: 0, explanation: "Undoubtedly expresses strong certainty about the importance of coming years." }
                ]
            ];

            return questionSets[Math.floor(Math.random() * questionSets.length)];
        }

        // Advanced content generators for different parts
        function generateAdvancedPart1Content(topic, uniqueId, timestamp) {
            const textVariations = [
                {
                    template: `The significance of ${topic} in contemporary society has become increasingly apparent. Recent research [GAP1] that this field is experiencing unprecedented growth. Scientists [GAP2] optimistic about future developments, [GAP3] there are still challenges to overcome. [GAP4] organizations worldwide are investing in innovative solutions. The [GAP5] of this technology depends on careful implementation. [GAP6] we must address ethical concerns, the potential benefits are substantial. [GAP7] collaboration will be essential for success. [GAP8] the next decade will be transformative.`,
                    gaps: [
                        {options: ["indicates", "suggests", "implies", "demonstrates"], correct: 0, explanation: "Indicates is the most direct way to present research findings."},
                        {options: ["remain", "become", "seem", "appear"], correct: 0, explanation: "Remain suggests continuity of optimism."},
                        {options: ["although", "because", "since", "unless"], correct: 0, explanation: "Although introduces a contrast between optimism and challenges."},
                        {options: ["Numerous", "Several", "Various", "Multiple"], correct: 0, explanation: "Numerous emphasizes the large quantity of organizations."},
                        {options: ["success", "failure", "impact", "development"], correct: 0, explanation: "Success is the most appropriate outcome measure."},
                        {options: ["While", "When", "Where", "Whether"], correct: 0, explanation: "While introduces a concession about addressing concerns."},
                        {options: ["International", "Global", "Worldwide", "Universal"], correct: 0, explanation: "International specifically refers to cooperation between nations."},
                        {options: ["Undoubtedly", "Certainly", "Definitely", "Clearly"], correct: 0, explanation: "Undoubtedly expresses strong certainty about the future."}
                    ]
                },
                {
                    template: `Understanding ${topic} requires a comprehensive approach that considers multiple perspectives. [GAP1] the complexity of the issue, significant progress has been made. [GAP2] studies reveal growing public awareness and engagement. The [GAP3] between theoretical knowledge and practical application remains challenging. [GAP4] stakeholders must collaborate to find effective solutions. [GAP5] the importance of education cannot be overlooked. [GAP6] we need innovative strategies and methodologies. The [GAP7] of technology plays a crucial role in advancement. [GAP8] success will depend on sustained commitment.`,
                    gaps: [
                        {options: ["Despite", "Because", "Through", "Without"], correct: 0, explanation: "Despite shows contrast between complexity and progress."},
                        {options: ["Recent", "Previous", "Current", "Ongoing"], correct: 0, explanation: "Recent indicates contemporary and relevant research."},
                        {options: ["gap", "bridge", "connection", "link"], correct: 0, explanation: "Gap indicates the space between theory and practice."},
                        {options: ["All", "Most", "Some", "Few"], correct: 0, explanation: "All emphasizes comprehensive stakeholder involvement."},
                        {options: ["Furthermore", "However", "Therefore", "Nevertheless"], correct: 0, explanation: "Furthermore adds additional important information."},
                        {options: ["Clearly", "Obviously", "Evidently", "Apparently"], correct: 0, explanation: "Clearly indicates that the need is evident."},
                        {options: ["role", "function", "purpose", "significance"], correct: 0, explanation: "Role is the most natural collocation with 'plays'."},
                        {options: ["Ultimate", "Final", "Complete", "Total"], correct: 0, explanation: "Ultimate suggests the most important success."}
                    ]
                }
            ];

            const selectedVariation = textVariations[timestamp % textVariations.length];

            return {
                title: `${topic.charAt(0).toUpperCase() + topic.slice(1)}: Modern Perspectives and Future Directions`,
                text: selectedVariation.template,
                questions: selectedVariation.gaps.map((gap, index) => ({
                    gapNumber: index + 1,
                    options: gap.options,
                    correctIndex: gap.correct,
                    explanation: gap.explanation
                }))
            };
        }

        function generateAdvancedPart2Content(topic, uniqueId, timestamp) {
            const textVariations = [
                {
                    template: `The role of ${topic} [GAP1] modern society continues to evolve rapidly. [GAP2] recent years, there has been significant progress in understanding [GAP3] implications. Researchers [GAP4] working to address the challenges [GAP5] arise from rapid technological advancement. [GAP6] is clear that collaboration between different sectors [GAP7] essential for success. [GAP8] the future, we can expect even more innovative solutions.`,
                    answers: ["in", "In", "its", "are", "that", "It", "is", "In"],
                    explanations: [
                        "Preposition 'in' is needed with 'role in society'",
                        "Temporal preposition 'In' starts the time phrase",
                        "Possessive pronoun 'its' refers back to the topic",
                        "Plural verb 'are' agrees with 'researchers'",
                        "Relative pronoun 'that' introduces the relative clause",
                        "Dummy subject 'It' is needed for the formal structure",
                        "Verb 'is' completes the 'is essential' structure",
                        "Preposition 'In' is used with future time reference"
                    ]
                }
            ];

            const selectedVariation = textVariations[timestamp % textVariations.length];

            return {
                title: `${topic.charAt(0).toUpperCase() + topic.slice(1)}: Contemporary Analysis`,
                text: selectedVariation.template,
                answers: selectedVariation.answers,
                explanations: selectedVariation.explanations
            };
        }

        function generateAdvancedPart3Content(topic, uniqueId, timestamp) {
            const wordSets = [
                {
                    template: `The ${topic} industry has undergone remarkable [GAP1] in recent years. This [GAP2] has led to numerous [GAP3] opportunities for both businesses and consumers. However, the [GAP4] of these changes requires careful consideration. Many [GAP5] believe that [GAP6] planning is essential for long-term success. The [GAP7] of sustainable practices has become increasingly important. [GAP8] solutions are needed to address current challenges.`,
                    words: [
                        {base: "TRANSFORM", answer: "transformation", explanation: "Noun form needed after 'remarkable'"},
                        {base: "DEVELOP", answer: "development", explanation: "Noun form needed as subject of sentence"},
                        {base: "INNOVATE", answer: "innovative", explanation: "Adjective form needed to modify 'opportunities'"},
                        {base: "IMPLEMENT", answer: "implementation", explanation: "Noun form needed after 'the'"},
                        {base: "SPECIAL", answer: "specialists", explanation: "Plural noun form needed as subject"},
                        {base: "STRATEGY", answer: "strategic", explanation: "Adjective form needed to modify 'planning'"},
                        {base: "ADOPT", answer: "adoption", explanation: "Noun form needed after 'the'"},
                        {base: "CREATE", answer: "creative", explanation: "Adjective form needed to modify 'solutions'"}
                    ]
                }
            ];

            const selectedSet = wordSets[timestamp % wordSets.length];

            return {
                title: `${topic.charAt(0).toUpperCase() + topic.slice(1)}: Transformation and Innovation`,
                text: selectedSet.template,
                questions: selectedSet.words.map((word, index) => ({
                    gapNumber: index + 1,
                    baseWord: word.base,
                    correctAnswer: word.answer,
                    explanation: word.explanation
                }))
            };
        }

        function getPartInstructions(partNumber) {
            const instructions = {
                1: 'For questions 1-8, read the text below and decide which answer (A, B, C or D) best fits each gap.',
                2: 'For questions 1-8, read the text below and think of the word which best fits each gap. Use only one word in each gap.',
                3: 'For questions 1-8, read the text below. Use the word given in capitals at the end of some of the lines to form a word that fits in the gap in the same line.',
                4: 'For questions 1-6, complete the second sentence so that it has a similar meaning to the first sentence, using the word given. Do not change the word given. You must use between two and five words, including the word given.',
                5: 'For questions 1-6, read the text below and answer the questions by choosing the correct answer A, B, C or D.',
                6: 'For questions 1-6, you are going to read a text that has six paragraphs missing. Choose from the paragraphs A-G the one which fits each gap. There is one extra paragraph which you do not need to use.',
                7: 'For questions 1-10, read the text below and match the information to the correct section. Some sections may be chosen more than once.'
            };
            return instructions[partNumber] || instructions[1];
        }

        function renderPartContent(partNumber, content) {
            switch(partNumber) {
                case 1:
                    return renderPart1Content(content);
                case 2:
                    return renderPart2Content(content);
                case 3:
                    return renderPart3Content(content);
                case 4:
                    return renderPart4Content(content);
                case 5:
                    return renderPart5Content(content);
                case 6:
                    return renderPart6Content(content);
                case 7:
                    return renderPart7Content(content);
                default:
                    return renderPart1Content(content);
            }
        }

        function renderPart1Content(content) {
            // Ensure content.questions exists, fallback to empty array
            const questions = content.questions || [];

            return `
                <!-- Split-Screen Layout: Text on Left, Questions on Right -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
                    <!-- Reading Text Panel (Left) -->
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-white pb-2 border-b">${content.title || 'Reading Text'}</h3>
                        <div class="text-gray-700 leading-relaxed">
                            ${(content.text || '').replace(/\[GAP(\d+)\]/g, (match, num) => {
                                return `<span class="bg-blue-600 text-white px-2 py-1 rounded font-medium mx-1">${num}</span>`;
                            })}
                        </div>
                    </div>

                    <!-- Questions Panel (Right) -->
                    <div class="bg-gray-50 border-2 border-blue-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-gray-50 pb-2 border-b text-blue-700">Answer Questions</h3>
                        <div class="space-y-4">
                            ${questions.map((q, index) => `
                                <div class="bg-white border border-blue-300 rounded-lg p-4">
                                    <h4 class="font-medium text-gray-700 mb-3 flex items-center">
                                        <span class="bg-blue-600 text-white px-2 py-1 rounded text-sm mr-2">${q.gapNumber || index + 1}</span>
                                        Question ${q.gapNumber || index + 1}
                                    </h4>
                                    <div class="space-y-2">
                                        ${(q.options || []).map((option, optionIndex) => `
                                            <label class="flex items-center space-x-3 p-2 hover:bg-blue-50 rounded cursor-pointer border border-transparent hover:border-blue-200">
                                                <input type="radio"
                                                       name="q${q.gapNumber || index + 1}"
                                                       value="${String.fromCharCode(65 + optionIndex)}"
                                                       onchange="saveAnswer('q${q.gapNumber || index + 1}', '${String.fromCharCode(65 + optionIndex)}', ${q.correctIndex || 0}, ${optionIndex})"
                                                       class="text-blue-600">
                                                <span><strong>${String.fromCharCode(65 + optionIndex)}</strong> ${option}</span>
                                            </label>
                                        `).join('')}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        function renderPart2Content(content) {
            // Ensure content.answers exists, fallback to empty array
            const answers = content.answers || [];

            return `
                <!-- Split-Screen Layout: Text on Left, Questions on Right -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
                    <!-- Reading Text Panel (Left) -->
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-white pb-2 border-b">${content.title || 'Reading Text'}</h3>
                        <div class="text-gray-700 leading-relaxed">
                            ${(content.text || '').replace(/\[GAP(\d+)\]/g, (match, num) => {
                                return `<span class="bg-green-600 text-white px-2 py-1 rounded font-medium mx-1">${num}</span>`;
                            })}
                        </div>
                    </div>

                    <!-- Questions Panel (Right) -->
                    <div class="bg-gray-50 border-2 border-green-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-gray-50 pb-2 border-b text-green-700">Fill in the Gaps</h3>
                        <div class="space-y-4">
                            ${answers.map((answer, index) => `
                                <div class="bg-white border border-green-300 rounded-lg p-4">
                                    <h4 class="font-medium text-gray-700 mb-3 flex items-center">
                                        <span class="bg-green-600 text-white px-2 py-1 rounded text-sm mr-2">${index + 1}</span>
                                        Question ${index + 1}
                                    </h4>
                                    <div class="space-y-2">
                                        <input type="text"
                                               name="q${index + 1}"
                                               placeholder="Enter one word"
                                               onchange="saveOpenAnswer('q${index + 1}', this.value, '${answer}')"
                                               class="w-full p-2 border border-green-300 rounded focus:border-green-500 focus:outline-none">
                                        <p class="text-xs text-gray-500">Enter exactly one word</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        function renderPart3Content(content) {
            // Ensure content.questions exists, fallback to empty array
            const questions = content.questions || [];

            return `
                <!-- Split-Screen Layout: Text on Left, Questions on Right -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
                    <!-- Reading Text Panel (Left) -->
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-white pb-2 border-b">${content.title || 'Reading Text'}</h3>
                        <div class="text-gray-700 leading-relaxed">
                            ${(content.text || '').replace(/\[GAP(\d+)\]/g, (match, num) => {
                                return `<span class="bg-purple-600 text-white px-2 py-1 rounded font-medium mx-1">${num}</span>`;
                            })}
                        </div>
                    </div>

                    <!-- Questions Panel (Right) -->
                    <div class="bg-gray-50 border-2 border-purple-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-gray-50 pb-2 border-b text-purple-700">Word Formation</h3>
                        <div class="space-y-4">
                            ${questions.map((q, index) => `
                                <div class="bg-white border border-purple-300 rounded-lg p-4">
                                    <h4 class="font-medium text-gray-700 mb-3 flex items-center">
                                        <span class="bg-purple-600 text-white px-2 py-1 rounded text-sm mr-2">${q.gapNumber || index + 1}</span>
                                        Question ${q.gapNumber || index + 1}
                                    </h4>
                                    <div class="space-y-2">
                                        <div class="flex items-center space-x-3 mb-2">
                                            <span class="font-medium text-purple-700">Base word:</span>
                                            <span class="bg-purple-100 px-2 py-1 rounded font-mono">${q.baseWord || 'WORD'}</span>
                                        </div>
                                        <input type="text"
                                               name="q${q.gapNumber || index + 1}"
                                               placeholder="Transform the word"
                                               onchange="saveWordFormationAnswer('q${q.gapNumber || index + 1}', this.value, '${q.correctAnswer || ''}')"
                                               class="w-full p-2 border border-purple-300 rounded focus:border-purple-500 focus:outline-none">
                                        <p class="text-xs text-gray-500">Use the correct form of ${q.baseWord || 'the base word'}</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        function renderPart4Content(content) {
            // Key Word Transformations
            const questions = content.questions || [];

            return `
                <div class="space-y-6">
                    <div class="p-4 bg-orange-50 border border-orange-500 rounded-lg">
                        <h4 class="text-orange-700 font-medium mb-2">Instructions</h4>
                        <p class="text-gray-700 text-sm">${content.instructions || 'Complete the second sentence using the key word.'}</p>
                    </div>

                    <div class="space-y-4">
                        ${questions.map((q, index) => `
                            <div class="bg-white border-2 border-orange-300 rounded-lg p-6">
                                <h4 class="font-medium text-gray-700 mb-3 flex items-center">
                                    <span class="bg-orange-600 text-white px-2 py-1 rounded text-sm mr-2">${q.questionNumber || index + 25}</span>
                                    Question ${q.questionNumber || index + 25}
                                </h4>

                                <div class="space-y-3">
                                    <p class="text-gray-700">${q.firstSentence}</p>
                                    <div class="text-center">
                                        <span class="bg-orange-100 px-3 py-1 rounded font-bold text-orange-700">${q.keyWord}</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <span class="text-gray-700">${q.secondSentence.split('[GAP')[0]}</span>
                                        <input type="text"
                                               name="q${q.questionNumber || index + 25}"
                                               placeholder="2-5 words including ${q.keyWord}"
                                               class="border-b-2 border-orange-300 px-2 py-1 focus:border-orange-500 focus:outline-none min-w-48"
                                               onchange="saveAnswer('q${q.questionNumber || index + 25}', this.value)">
                                        <span class="text-gray-700">${q.secondSentence.split(']')[1] || ''}</span>
                                    </div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        }

        function renderPart5Content(content) {
            // Multiple Choice Reading Comprehension
            const questions = content.questions || [];

            return `
                <!-- Split-Screen Layout: Text on Left, Questions on Right -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
                    <!-- Reading Text Panel (Left) -->
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-white pb-2 border-b">${content.title || 'Reading Text'}</h3>
                        <div class="text-gray-700 leading-relaxed text-sm">
                            ${content.text || 'No text available'}
                        </div>
                    </div>

                    <!-- Questions Panel (Right) -->
                    <div class="bg-gray-50 border-2 border-green-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-gray-50 pb-2 border-b text-green-700">Multiple Choice Questions</h3>
                        <div class="space-y-4">
                            ${questions.map((q, index) => `
                                <div class="bg-white border border-green-300 rounded-lg p-4">
                                    <h4 class="font-medium text-gray-700 mb-3 flex items-center">
                                        <span class="bg-green-600 text-white px-2 py-1 rounded text-sm mr-2">${q.questionNumber || index + 31}</span>
                                        Question ${q.questionNumber || index + 31}
                                    </h4>
                                    <p class="text-sm text-gray-700 mb-3">${q.question}</p>
                                    <div class="space-y-2">
                                        ${(q.options || []).map((option, optIndex) => `
                                            <label class="flex items-center space-x-3 p-2 hover:bg-green-50 rounded cursor-pointer">
                                                <input type="radio" name="q${q.questionNumber || index + 31}" value="${optIndex}"
                                                       onchange="saveAnswer('q${q.questionNumber || index + 31}', '${String.fromCharCode(65 + optIndex)}', ${q.correctIndex}, ${optIndex})"
                                                       class="text-green-600">
                                                <span><strong>${String.fromCharCode(65 + optIndex)}</strong> ${option}</span>
                                            </label>
                                        `).join('')}
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            `;
        }

        function renderPart6Content(content) {
            // Gapped Text
            const options = content.options || [];

            return `
                <!-- Split-Screen Layout: Text on Left, Options on Right -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
                    <!-- Reading Text Panel (Left) -->
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-white pb-2 border-b">${content.title || 'Reading Text'}</h3>
                        <div class="text-gray-700 leading-relaxed text-sm">
                            ${(content.text || '').replace(/\[GAP (\d+)\]/g, (match, num) => {
                                return `<div class="my-4 p-3 bg-indigo-100 border-2 border-indigo-300 rounded-lg">
                                    <span class="bg-indigo-600 text-white px-2 py-1 rounded font-medium">Gap ${num}</span>
                                    <select name="gap${num}" class="ml-2 border border-indigo-300 rounded px-2 py-1" onchange="saveAnswer('gap${num}', this.value)">
                                        <option value="">Choose sentence...</option>
                                        ${options.map((opt, idx) => `<option value="${String.fromCharCode(65 + idx)}">${String.fromCharCode(65 + idx)}</option>`).join('')}
                                    </select>
                                </div>`;
                            })}
                        </div>
                    </div>

                    <!-- Options Panel (Right) -->
                    <div class="bg-gray-50 border-2 border-indigo-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-gray-50 pb-2 border-b text-indigo-700">Sentence Options</h3>
                        <div class="space-y-3">
                            ${options.map((option, index) => `
                                <div class="bg-white border border-indigo-300 rounded-lg p-3">
                                    <div class="flex items-start space-x-2">
                                        <span class="bg-indigo-600 text-white px-2 py-1 rounded text-sm font-bold">${String.fromCharCode(65 + index)}</span>
                                        <p class="text-sm text-gray-700 flex-1">${option.replace(/\${topic}/g, content.topic || 'the subject')}</p>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-300 rounded">
                            <p class="text-xs text-yellow-700">Note: There is one extra sentence which you do not need to use.</p>
                        </div>
                    </div>
                </div>
            `;
        }

        function renderPart7Content(content) {
            // Multiple Matching
            const paragraphs = content.paragraphs || [];
            const questions = content.questions || [];

            return `
                <!-- Split-Screen Layout: Text on Left, Questions on Right -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 h-96">
                    <!-- Reading Text Panel (Left) -->
                    <div class="bg-white border-2 border-gray-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-white pb-2 border-b">${content.title || 'Reading Text'}</h3>
                        <p class="text-sm text-gray-600 mb-4">${content.subtitle || ''}</p>
                        <div class="space-y-4">
                            ${paragraphs.map(para => `
                                <div class="border-l-4 border-red-500 pl-4">
                                    <h4 class="font-bold text-red-600 mb-2">Paragraph ${para.letter}</h4>
                                    <p class="text-gray-700 text-sm leading-relaxed">${para.text}</p>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Questions Panel (Right) -->
                    <div class="bg-gray-50 border-2 border-red-300 rounded-lg p-6 overflow-y-auto">
                        <h3 class="text-lg font-semibold mb-4 sticky top-0 bg-gray-50 pb-2 border-b text-red-700">Which paragraph...</h3>
                        <div class="space-y-3">
                            ${questions.map((q, index) => `
                                <div class="bg-white border border-red-300 rounded-lg p-3">
                                    <div class="flex items-start space-x-3">
                                        <span class="bg-red-600 text-white px-2 py-1 rounded text-sm font-bold">${q.questionNumber || index + 43}</span>
                                        <div class="flex-1">
                                            <p class="text-sm text-gray-700 mb-2">${q.question}</p>
                                            <select name="q${q.questionNumber || index + 43}" class="border border-red-300 rounded px-2 py-1" onchange="saveAnswer('q${q.questionNumber || index + 43}', this.value)">
                                                <option value="">Choose...</option>
                                                ${paragraphs.map(para => `<option value="${para.letter}">Paragraph ${para.letter}</option>`).join('')}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                        <div class="mt-4 p-3 bg-yellow-50 border border-yellow-300 rounded">
                            <p class="text-xs text-yellow-700">Note: Each answer may be chosen more than once.</p>
                        </div>
                    </div>
                </div>
            `;
        }

        async function generateAndShowWriting(partNumber = 1, taskType = 'essay') {
            const startTime = Date.now();
            console.log(`🚀 Starting Writing Part ${partNumber} (${taskType}) generation at ${startTime}`);

            const taskName = partNumber === 1 ? 'Essay' : taskType.charAt(0).toUpperCase() + taskType.slice(1);
            showLoadingScreen(`✍️ Generating Writing ${taskName}...`, `Google Gemini AI is creating a personalized ${taskName.toLowerCase()} task!`);

            try {
                const generatedText = await generateWritingContent(partNumber, taskType);
                console.log('📥 Raw Gemini response received:', generatedText.substring(0, 200) + '...');

                try {
                    // Clean the response - remove any markdown formatting
                    let cleanText = generatedText.trim();
                    if (cleanText.startsWith('```json')) {
                        cleanText = cleanText.replace(/```json\n?/, '').replace(/\n?```$/, '');
                    }
                    if (cleanText.startsWith('```')) {
                        cleanText = cleanText.replace(/```\n?/, '').replace(/\n?```$/, '');
                    }

                    generatedContent = JSON.parse(cleanText);
                    generatedContent.isAIGenerated = true;
                    generatedContent.generationTime = Date.now() - startTime;
                    generatedContent.partNumber = partNumber;
                    generatedContent.taskType = taskType;

                    console.log('✅ Successfully parsed writing content:', generatedContent);
                    console.log(`⏱️ Generation took ${generatedContent.generationTime}ms`);
                    showGeneratedWriting();
                } catch (parseError) {
                    console.error('❌ Failed to parse JSON:', parseError);
                    console.log('Raw text that failed to parse:', generatedText);
                    console.log('🔄 Falling back to static content...');
                    generateFallbackWriting(partNumber, taskType);
                }

            } catch (error) {
                console.error('❌ Failed to generate content:', error);
                console.log('🔄 Falling back to static content...');
                generateFallbackWriting(partNumber, taskType);
            }
        }

        function showLoadingScreen(title, description) {
            document.getElementById('modalTitle').textContent = 'AI Content Generation';
            document.getElementById('modalContent').innerHTML = `
                <div class="text-center py-12">
                    <div class="mb-6">
                        <div class="animate-spin rounded-full h-16 w-16 border-b-4 border-blue-600 mx-auto"></div>
                    </div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-4">${title}</h3>
                    <p class="text-gray-600 mb-6">${description}</p>
                    <div class="max-w-md mx-auto">
                        <div class="bg-gray-200 rounded-full h-2">
                            <div class="bg-blue-600 h-2 rounded-full animate-pulse" style="width: 60%"></div>
                        </div>
                        <p class="text-sm text-gray-500 mt-2">This may take a few seconds...</p>
                    </div>
                </div>
            `;
            openModal();
        }

        function showGeneratedReading() {
            if (!generatedContent) {
                showReadingDemo();
                return;
            }

            const partNumber = generatedContent.partNumber || 1;
            const partNames = {
                1: 'Multiple Choice Cloze',
                2: 'Open Cloze',
                3: 'Word Formation',
                4: 'Key Word Transformation',
                5: 'Multiple Choice Reading',
                6: 'Gapped Text',
                7: 'Multiple Matching'
            };

            document.getElementById('modalTitle').textContent = `Reading & Use of English - Part ${partNumber}: ${partNames[partNumber]}`;
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <!-- Enhanced AI Generated Badge with Verification -->
                    <div class="p-4 ${generatedContent.isAIGenerated ? 'bg-green-50 border-green-500' : 'bg-blue-50 border-blue-500'} border rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="${generatedContent.isAIGenerated ? 'text-green-600' : 'text-blue-600'}">${generatedContent.isAIGenerated ? '🤖' : '🔧'}</span>
                                <span class="${generatedContent.isAIGenerated ? 'text-green-700' : 'text-blue-700'} font-medium">
                                    ${generatedContent.isAIGenerated ? 'AI Generated Content' : 'AI-Simulated Content'}
                                </span>
                                <span class="px-2 py-1 ${generatedContent.isAIGenerated ? 'bg-green-600' : 'bg-blue-600'} text-white text-xs rounded">
                                    ${generatedContent.isAIGenerated ? 'REAL AI' : 'FALLBACK'}
                                </span>
                            </div>
                            <button onclick="showGenerationDetails('reading')" class="text-xs ${generatedContent.isAIGenerated ? 'text-green-600 hover:text-green-800' : 'text-blue-600 hover:text-blue-800'}">Details</button>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Topic:</span>
                                <span class="font-medium ${generatedContent.isAIGenerated ? 'text-green-700' : 'text-blue-700'}">${generatedContent.topic}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Generated:</span>
                                <span class="font-medium ${generatedContent.isAIGenerated ? 'text-green-700' : 'text-blue-700'}">${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Generation #:</span>
                                <span class="font-medium ${generatedContent.isAIGenerated ? 'text-green-700' : 'text-blue-700'}">${generationCounter}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Unique ID:</span>
                                <span class="font-mono text-xs ${generatedContent.isAIGenerated ? 'text-green-700' : 'text-blue-700'}">${generatedContent.generationId || 'N/A'}</span>
                            </div>
                            ${generatedContent.isAIGenerated ? `
                                <div>
                                    <span class="text-gray-600">AI Time:</span>
                                    <span class="font-medium text-green-700">${generatedContent.generationTime}ms</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Status:</span>
                                    <span class="font-medium text-green-700">✅ Success</span>
                                </div>
                            ` : `
                                <div>
                                    <span class="text-gray-600">Reason:</span>
                                    <span class="font-medium text-blue-700">API Unavailable</span>
                                </div>
                                <div>
                                    <span class="text-gray-600">Status:</span>
                                    <span class="font-medium text-blue-700">🔧 Fallback</span>
                                </div>
                            `}
                        </div>
                    </div>

                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">Part ${partNumber}: ${partNames[partNumber]}</h4>
                        <p class="text-gray-700 text-sm">${getPartInstructions(partNumber)}</p>
                    </div>

                    ${renderPartContent(partNumber, generatedContent)}

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">AI Generated • Questions 1-8 of 8 • Part 1</span>
                        <div class="space-x-2">
                            <button onclick="showAllExplanations()" class="btn-secondary">Show Explanations</button>
                            <button onclick="showResults()" class="btn-primary">Finish & Review</button>
                            <button onclick="generateAndShowReading(${partNumber})" class="btn-primary">🔄 Generate New</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function showGeneratedWriting() {
            if (!generatedContent) {
                showWritingDemo();
                return;
            }

            document.getElementById('modalTitle').textContent = 'Writing - Essay (AI Generated)';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <!-- Enhanced AI Generated Badge with Verification -->
                    <div class="p-4 bg-green-50 border border-green-500 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <div class="flex items-center space-x-2">
                                <span class="text-green-600">🤖</span>
                                <span class="text-green-700 font-medium">AI Generated Content</span>
                                <span class="px-2 py-1 bg-green-600 text-white text-xs rounded">NEW</span>
                            </div>
                            <button onclick="showGenerationDetails('writing')" class="text-xs text-green-600 hover:text-green-800">Details</button>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Topic:</span>
                                <span class="font-medium text-green-700">${generatedContent.topic}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Generated:</span>
                                <span class="font-medium text-green-700">${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Generation #:</span>
                                <span class="font-medium text-green-700">${generationCounter}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Unique ID:</span>
                                <span class="font-mono text-xs text-green-700">${generatedContent.generationId || 'N/A'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 bg-green-50 border border-green-500 rounded-lg">
                        <h4 class="text-green-700 font-medium mb-2">Part 1: Essay (Compulsory)</h4>
                        <p class="text-gray-700 text-sm">Write an essay of <strong>140-190 words</strong> using all the notes provided.</p>
                    </div>

                    <div class="p-6 bg-white border-2 border-gray-200 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4">Essay Topic:</h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            <strong>${generatedContent.essayPrompt}</strong>
                        </p>
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-medium mb-2">Write about:</h4>
                            <ul class="space-y-1 text-sm">
                                ${generatedContent.bulletPoints.map((point, index) => `
                                    <li>${index + 1}. ${point}</li>
                                `).join('')}
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-700">Your Essay:</h4>
                            <div class="text-sm font-medium" id="wordCount">0 / 140-190 words</div>
                        </div>

                        <textarea
                            id="essayText"
                            class="w-full h-64 p-4 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                            placeholder="Start writing your essay here..."
                            oninput="updateWordCount()"
                        ></textarea>

                        <div class="grid grid-cols-4 gap-4 text-sm text-center">
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-blue-600" id="wordCountDisplay">0</div>
                                <div class="text-gray-600">Words</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-green-600" id="paragraphCount">0</div>
                                <div class="text-gray-600">Paragraphs</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-purple-600" id="sentenceCount">0</div>
                                <div class="text-gray-600">Sentences</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-orange-600" id="progressPercent">0%</div>
                                <div class="text-gray-600">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">AI Generated • Part 1 • Essay</span>
                        <div class="space-x-2">
                            <button onclick="submitEssay()" class="btn-primary">Submit Essay</button>
                            <button onclick="generateAndShowWriting()" class="btn-primary">🔄 Generate New Topic</button>
                        </div>
                    </div>
                </div>
            `;
        }

        function showSettings() {
            document.getElementById('modalTitle').textContent = 'Settings & Debug Panel';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-3">🤖 Gemini API Configuration</h4>
                        <div class="space-y-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">API Key:</label>
                                <input type="password" id="geminiApiKey" placeholder="Enter your Gemini API key"
                                       value="${window.geminiApiKey || ''}"
                                       class="w-full p-2 border border-gray-300 rounded focus:border-blue-500 focus:outline-none">
                                <p class="text-xs text-gray-500 mt-1">Get your API key from Google AI Studio</p>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="saveApiKey()" class="bg-blue-500 text-white px-3 py-1 rounded text-sm hover:bg-blue-600">Save</button>
                                <button onclick="testGeminiConnection()" class="bg-green-500 text-white px-3 py-1 rounded text-sm hover:bg-green-600">Test Connection</button>
                            </div>
                        </div>
                    </div>

                    <div class="p-4 bg-gray-50 border border-gray-300 rounded-lg">
                        <h4 class="text-gray-700 font-medium mb-2">⚙️ Application Settings</h4>
                        <div class="space-y-2 text-sm">
                            <p>• Timer preferences</p>
                            <p>• Difficulty levels</p>
                            <p>• Notification settings</p>
                            <p>• AI generation preferences</p>
                        </div>
                    </div>

                    <div class="p-4 bg-green-50 border border-green-500 rounded-lg">
                        <h4 class="text-green-700 font-medium mb-3">🤖 AI Generation Status</h4>
                        <div class="grid grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-gray-600">Session ID:</span>
                                <span class="font-mono text-xs">${sessionId}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Total Generations:</span>
                                <span class="font-medium">${generationHistory.length}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Successful:</span>
                                <span class="font-medium text-green-600">${generationHistory.filter(h => h.success).length}</span>
                            </div>
                            <div>
                                <span class="text-gray-600">Failed:</span>
                                <span class="font-medium text-red-600">${generationHistory.filter(h => !h.success).length}</span>
                            </div>
                        </div>

                        <div class="mt-4 space-x-2">
                            <button onclick="showGenerationDetails('all')" class="btn-secondary text-xs">View All History</button>
                            <button onclick="resetGenerationHistory()" class="btn-secondary text-xs">Reset History</button>
                            <button onclick="testGeminiConnection()" class="btn-primary text-xs">Test AI Connection</button>
                        </div>
                    </div>

                    <div class="p-4 bg-yellow-50 border border-yellow-500 rounded-lg">
                        <h4 class="text-yellow-700 font-medium mb-2">🔍 Debug Information</h4>
                        <div class="text-xs space-y-1">
                            <p><strong>Last Generation:</strong> ${lastGenerationTime ? new Date(lastGenerationTime).toLocaleString() : 'None'}</p>
                            <p><strong>Generation Counter:</strong> ${generationCounter}</p>
                            <p><strong>Current Content:</strong> ${generatedContent ? 'Loaded' : 'None'}</p>
                            <p><strong>API Key Status:</strong> ${GEMINI_API_KEY ? 'Configured' : 'Missing'}</p>
                        </div>
                    </div>

                    <div class="flex justify-center">
                        <button onclick="closeModal()" class="btn-primary">Close Settings</button>
                    </div>
                </div>
            `;
            openModal();
        }

        function saveApiKey() {
            const apiKeyInput = document.getElementById('geminiApiKey');
            const apiKey = apiKeyInput.value.trim();

            if (apiKey) {
                window.geminiApiKey = apiKey;
                localStorage.setItem('geminiApiKey', apiKey);
                alert('✅ API Key saved successfully!\n\nYou can now use Gemini AI to generate exercises.');
            } else {
                alert('❌ Please enter a valid API key.');
            }
        }

        // Load API key on page load
        window.addEventListener('load', function() {
            const savedApiKey = localStorage.getItem('geminiApiKey');
            if (savedApiKey) {
                window.geminiApiKey = savedApiKey;
                console.log('🔑 Gemini API key loaded from storage');
            }
        });

        async function testGeminiConnection() {
            try {
                console.log('🧪 Testing Gemini API connection...');

                if (!window.geminiApiKey) {
                    alert('❌ API Key Missing\n\nPlease enter your Gemini API key first.');
                    return;
                }

                // Test Gemini API directly
                const testContent = await generateContentWithGemini('reading', 1, 'artificial intelligence', 'test_' + Date.now());

                if (testContent && testContent.generationId) {
                    alert('✅ Gemini API Test: SUCCESS\n\nThe Gemini API is working correctly and generating unique content.\n\nGenerated content includes:\n- Unique ID: ' + testContent.generationId + '\n- Topic: ' + testContent.topic + '\n- Part: ' + testContent.partNumber + '\n- AI Generated: ' + testContent.isAIGenerated);
                } else {
                    alert('❌ Gemini API Test: FAILED\n\nThe API could not generate content. Please check your API key and internet connection.');
                }
            } catch (error) {
                console.error('❌ Gemini API test failed:', error);
                if (error.message.includes('CORS')) {
                    alert('❌ CORS Error\n\nDirect API calls are blocked by browser security.\n\nThe app will use local generation as fallback.\n\nFor full Gemini API support, you need a backend server.');
                } else if (error.message.includes('API key')) {
                    alert('❌ API Key Error\n\nPlease check your Gemini API key is correct.\n\nGet your key from: https://makersuite.google.com/app/apikey');
                } else {
                    alert('❌ Gemini API Test: ERROR\n\nError: ' + error.message + '\n\nThe app will use local generation as fallback.');
                }
            }
        }

        function showProgress() {
            alert('📊 Progress & Statistics\n\nProgress panel would show:\n• Performance analytics\n• Strengths and weaknesses\n• Study recommendations');
        }

        function showReadingDemo() {
            console.log('showReadingDemo called');
            document.getElementById('modalTitle').textContent = 'Reading & Use of English - Part 1';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">Part 1: Multiple Choice Cloze</h4>
                        <p class="text-gray-700 text-sm">For questions 1-8, read the text below and decide which answer (A, B, C or D) best fits each gap.</p>
                    </div>

                    <div class="prose max-w-none">
                        <h3 class="text-lg font-semibold mb-4">The Digital Revolution</h3>
                        <p class="text-gray-700 leading-relaxed">
                            The internet has fundamentally changed how we communicate and access information.
                            Social media platforms have <span class="bg-blue-600 text-white px-2 py-1 rounded font-medium">1</span>
                            people to connect with others around the world instantly. However, this technological advancement
                            has also <span class="bg-blue-600 text-white px-2 py-1 rounded font-medium">2</span>
                            new challenges for society.
                        </p>
                    </div>

                    <div class="space-y-4">
                        <div class="border-2 border-blue-500 rounded-lg p-4 bg-blue-50">
                            <h4 class="font-medium text-gray-700 mb-3">Question 1:</h4>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="A" onchange="saveAnswer('q1', 'A')" class="text-blue-600">
                                    <span><strong>A</strong> enabled</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="B" onchange="saveAnswer('q1', 'B')" class="text-blue-600">
                                    <span><strong>B</strong> allowed</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="C" onchange="saveAnswer('q1', 'C')" class="text-blue-600">
                                    <span><strong>C</strong> permitted</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-white rounded cursor-pointer">
                                    <input type="radio" name="q1" value="D" onchange="saveAnswer('q1', 'D')" class="text-blue-600">
                                    <span><strong>D</strong> let</span>
                                </label>
                            </div>
                        </div>

                        <div class="border-2 border-purple-500 rounded-lg p-4">
                            <h4 class="font-medium text-gray-700 mb-3">Question 2:</h4>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="A" onchange="saveAnswer('q2', 'A')" class="text-purple-600">
                                    <span><strong>A</strong> created</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="B" onchange="saveAnswer('q2', 'B')" class="text-purple-600">
                                    <span><strong>B</strong> made</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="C" onchange="saveAnswer('q2', 'C')" class="text-purple-600">
                                    <span><strong>C</strong> formed</span>
                                </label>
                                <label class="flex items-center space-x-3 p-2 hover:bg-purple-50 rounded cursor-pointer">
                                    <input type="radio" name="q2" value="D" onchange="saveAnswer('q2', 'D')" class="text-purple-600">
                                    <span><strong>D</strong> built</span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">Question 1-2 of 8 • Part 1 of 7</span>
                        <div class="space-x-2">
                            <button onclick="flagQuestion()" class="btn-secondary">🏳️ Flag for Review</button>
                            <button onclick="nextQuestion()" class="btn-primary">Next Question</button>
                            <button onclick="showResults()" class="btn-primary">Finish & Review</button>
                        </div>
                    </div>
                </div>
            `;
            openModal();
        }

        function showWritingDemo() {
            console.log('showWritingDemo called');
            document.getElementById('modalTitle').textContent = 'Writing - Essay';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <div class="p-4 bg-green-50 border border-green-500 rounded-lg">
                        <h4 class="text-green-700 font-medium mb-2">Part 1: Essay (Compulsory)</h4>
                        <p class="text-gray-700 text-sm">Write an essay of <strong>140-190 words</strong> using all the notes provided.</p>
                    </div>

                    <div class="p-6 bg-white border-2 border-gray-200 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-700 mb-4">Essay Topic:</h3>
                        <p class="text-gray-700 leading-relaxed mb-4">
                            <strong>Technology has made our lives easier, but has it made us happier? Discuss.</strong>
                        </p>
                        <div class="bg-gray-50 p-4 rounded">
                            <h4 class="font-medium mb-2">Write about:</h4>
                            <ul class="space-y-1 text-sm">
                                <li>1. Communication and relationships</li>
                                <li>2. Work and productivity</li>
                                <li>3. ........................... (your own idea)</li>
                            </ul>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h4 class="text-sm font-medium text-gray-700">Your Essay:</h4>
                            <div class="text-sm font-medium" id="wordCount">0 / 140-190 words</div>
                        </div>

                        <textarea
                            id="essayText"
                            class="w-full h-64 p-4 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent resize-none"
                            placeholder="Start writing your essay here..."
                            oninput="updateWordCount()"
                        ></textarea>

                        <div class="grid grid-cols-4 gap-4 text-sm text-center">
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-blue-600" id="wordCountDisplay">0</div>
                                <div class="text-gray-600">Words</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-green-600" id="paragraphCount">0</div>
                                <div class="text-gray-600">Paragraphs</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-purple-600" id="sentenceCount">0</div>
                                <div class="text-gray-600">Sentences</div>
                            </div>
                            <div class="p-3 bg-gray-50 rounded">
                                <div class="text-2xl font-bold text-orange-600" id="progressPercent">0%</div>
                                <div class="text-gray-600">Complete</div>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between items-center pt-4 border-t">
                        <span class="text-sm text-gray-600">Part 1 • Essay (Compulsory)</span>
                        <div class="space-x-2">
                            <button onclick="saveDraft()" class="btn-secondary">Save Draft</button>
                            <button onclick="submitEssay()" class="btn-primary">Submit Essay</button>
                        </div>
                    </div>
                </div>
            `;
            openModal();
        }

        function saveAnswer(questionId, answer, correctIndex, selectedIndex) {
            console.log('Answer saved:', questionId, answer);
            userAnswers[questionId] = {
                answer: answer,
                isCorrect: correctIndex === selectedIndex,
                correctIndex: correctIndex,
                selectedIndex: selectedIndex
            };

            // DO NOT show explanations during test - only after completion
            // Explanations will be shown in the review phase
        }

        function saveOpenAnswer(questionId, userAnswer, correctAnswer) {
            console.log('Open answer saved:', questionId, userAnswer);
            const isCorrect = userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();
            userAnswers[questionId] = {
                answer: userAnswer,
                correctAnswer: correctAnswer,
                isCorrect: isCorrect,
                type: 'open'
            };
        }

        function saveWordFormationAnswer(questionId, userAnswer, correctAnswer) {
            console.log('Word formation answer saved:', questionId, userAnswer);
            const isCorrect = userAnswer.toLowerCase().trim() === correctAnswer.toLowerCase().trim();
            userAnswers[questionId] = {
                answer: userAnswer,
                correctAnswer: correctAnswer,
                isCorrect: isCorrect,
                type: 'wordformation'
            };
        }

        function showAllExplanations() {
            // Show all explanation divs
            document.querySelectorAll('[id^="explanation"]').forEach(el => {
                el.classList.remove('hidden');
            });
        }

        function showGenerationDetails(contentType) {
            const history = generationHistory.filter(h => h.contentType === contentType);
            const currentGeneration = history[history.length - 1];

            alert(`🔍 Generation Details

📊 Current Generation:
• Type: ${contentType}
• Generation #: ${generationCounter}
• Session ID: ${sessionId}
• Request ID: ${currentGeneration?.requestId || 'N/A'}
• Topic: ${currentGeneration?.topic || 'N/A'}
• Success: ${currentGeneration?.success ? '✅' : '❌'}
• Response Length: ${currentGeneration?.responseLength || 0} chars

📈 Session Statistics:
• Total Generations: ${generationHistory.length}
• Successful: ${generationHistory.filter(h => h.success).length}
• Failed: ${generationHistory.filter(h => !h.success).length}
• Reading Generations: ${generationHistory.filter(h => h.contentType === 'reading').length}
• Writing Generations: ${generationHistory.filter(h => h.contentType === 'writing').length}

🕒 Recent History:
${history.slice(-3).map(h => `• ${new Date(h.timestamp).toLocaleTimeString()}: ${h.topic} (${h.success ? 'Success' : 'Failed'})`).join('\n')}

This confirms that new content is being generated each time!`);
        }

        function resetGenerationHistory() {
            generationHistory = [];
            generationCounter = 0;
            sessionId = Date.now() + Math.random().toString(36).substr(2, 9);
            console.log('🔄 Generation history reset. New session:', sessionId);
        }

        function updateWordCount() {
            const textarea = document.getElementById('essayText');
            if (!textarea) return;

            const text = textarea.value;
            const words = text.trim().split(/\s+/).filter(word => word.length > 0);
            const paragraphs = text.split('\n\n').filter(p => p.trim()).length;
            const sentences = text.split(/[.!?]+/).filter(s => s.trim()).length;

            const wordCount = words.length;
            const progress = Math.round((wordCount / 190) * 100);

            document.getElementById('wordCountDisplay').textContent = wordCount;
            document.getElementById('paragraphCount').textContent = paragraphs;
            document.getElementById('sentenceCount').textContent = sentences;
            document.getElementById('progressPercent').textContent = progress + '%';

            const wordCountEl = document.getElementById('wordCount');
            if (wordCount < 140) {
                wordCountEl.textContent = `${wordCount} / 140-190 words`;
                wordCountEl.className = 'text-sm font-medium text-orange-600';
            } else if (wordCount > 190) {
                wordCountEl.textContent = `${wordCount} / 140-190 words`;
                wordCountEl.className = 'text-sm font-medium text-red-600';
            } else {
                wordCountEl.textContent = `${wordCount} / 140-190 words`;
                wordCountEl.className = 'text-sm font-medium text-green-600';
            }
        }

        function flagQuestion() {
            alert('🏳️ Question flagged for review!');
        }

        function nextQuestion() {
            alert('➡️ Moving to next question...');
        }

        function showResults() {
            console.log('showResults called');
            const reviewData = calculateDetailedResults();

            document.getElementById('modalTitle').textContent = 'Comprehensive Test Review';
            document.getElementById('modalContent').innerHTML = `
                <div class="space-y-6">
                    <!-- Performance Summary -->
                    <div class="text-center p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border">
                        <h3 class="text-2xl font-bold text-gray-700 mb-2">🎉 Test Completed!</h3>
                        <p class="text-gray-600 mb-4">Here's your comprehensive performance analysis</p>

                        <div class="grid grid-cols-4 gap-4">
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-blue-600">${reviewData.totalQuestions}</div>
                                <div class="text-xs text-gray-600">Total Questions</div>
                            </div>
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-green-600">${reviewData.correctAnswers}</div>
                                <div class="text-xs text-gray-600">Correct</div>
                            </div>
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-red-600">${reviewData.incorrectAnswers}</div>
                                <div class="text-xs text-gray-600">Incorrect</div>
                            </div>
                            <div class="text-center p-3 bg-white rounded-lg shadow-sm">
                                <div class="text-2xl font-bold text-purple-600">${reviewData.scorePercentage}%</div>
                                <div class="text-xs text-gray-600">Score</div>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Level -->
                    <div class="p-4 ${reviewData.levelColor} rounded-lg border">
                        <div class="flex items-center justify-between">
                            <div>
                                <h4 class="font-bold text-lg">${reviewData.levelIcon} ${reviewData.levelName}</h4>
                                <p class="text-sm">${reviewData.levelDescription}</p>
                            </div>
                            <div class="text-right">
                                <div class="text-2xl font-bold">${reviewData.cefr}</div>
                                <div class="text-xs">CEFR Level</div>
                            </div>
                        </div>
                    </div>

                    <!-- Detailed Question Review -->
                    <div class="space-y-4">
                        <h4 class="text-lg font-bold text-gray-700 border-b pb-2">📋 Detailed Answer Review</h4>
                        ${generateDetailedReview(reviewData)}
                    </div>

                    <!-- Areas for Improvement -->
                    <div class="p-4 bg-yellow-50 border border-yellow-500 rounded-lg">
                        <h4 class="text-yellow-700 font-medium mb-2">💡 Areas for Improvement</h4>
                        <div class="space-y-2 text-sm">
                            ${reviewData.improvements.map(improvement => `
                                <div class="flex items-start space-x-2">
                                    <span class="text-yellow-600">•</span>
                                    <span>${improvement}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <!-- Study Recommendations -->
                    <div class="p-4 bg-blue-50 border border-blue-500 rounded-lg">
                        <h4 class="text-blue-700 font-medium mb-2">📚 Study Recommendations</h4>
                        <div class="space-y-2 text-sm">
                            ${reviewData.recommendations.map(rec => `
                                <div class="flex items-start space-x-2">
                                    <span class="text-blue-600">•</span>
                                    <span>${rec}</span>
                                </div>
                            `).join('')}
                        </div>
                    </div>

                    <div class="flex justify-center space-x-4">
                        <button onclick="startNewTest()" class="btn-primary">Start New Test</button>
                        <button onclick="exportResults()" class="btn-secondary">Export Results</button>
                        <button onclick="closeModal()" class="btn-secondary">Back to Dashboard</button>
                    </div>
                </div>
            `;
        }

        function saveDraft() {
            alert('✅ Draft saved successfully!');
        }

        function submitEssay() {
            alert('📝 Essay submitted for review!');
            showResults();
        }

        function calculateDetailedResults() {
            if (!generatedContent || !generatedContent.questions) {
                return {
                    totalQuestions: 0,
                    correctAnswers: 0,
                    incorrectAnswers: 0,
                    scorePercentage: 0,
                    levelName: 'No Data',
                    levelDescription: 'Complete a test to see results',
                    levelColor: 'bg-gray-50',
                    levelIcon: '❓',
                    cefr: 'N/A',
                    improvements: ['Complete a test to get personalized feedback'],
                    recommendations: ['Take a practice test to begin your learning journey']
                };
            }

            const questions = generatedContent.questions;
            let correctCount = 0;
            let totalCount = questions.length;
            const detailedAnswers = [];

            questions.forEach((question, index) => {
                const questionId = `q${question.gapNumber}`;
                const userAnswer = userAnswers[questionId];

                if (userAnswer) {
                    const isCorrect = userAnswer.isCorrect;
                    if (isCorrect) correctCount++;

                    detailedAnswers.push({
                        questionNumber: question.gapNumber,
                        userAnswer: userAnswer.answer,
                        correctAnswer: String.fromCharCode(65 + question.correctIndex),
                        isCorrect: isCorrect,
                        explanation: question.explanation,
                        options: question.options
                    });
                }
            });

            const scorePercentage = totalCount > 0 ? Math.round((correctCount / totalCount) * 100) : 0;

            // Determine CEFR level and feedback
            let levelData;
            if (scorePercentage >= 85) {
                levelData = {
                    levelName: 'Excellent Performance',
                    levelDescription: 'You demonstrate strong B2+ level skills',
                    levelColor: 'bg-green-50 border-green-500',
                    levelIcon: '🌟',
                    cefr: 'B2+',
                    improvements: [
                        'Continue practicing to maintain your high level',
                        'Consider advancing to C1 level materials',
                        'Focus on more complex grammatical structures'
                    ],
                    recommendations: [
                        'Practice C1 level reading materials',
                        'Work on advanced vocabulary and collocations',
                        'Try more challenging authentic texts'
                    ]
                };
            } else if (scorePercentage >= 70) {
                levelData = {
                    levelName: 'Good Performance',
                    levelDescription: 'You are at a solid B2 level',
                    levelColor: 'bg-blue-50 border-blue-500',
                    levelIcon: '👍',
                    cefr: 'B2',
                    improvements: [
                        'Review incorrect answers carefully',
                        'Practice more complex sentence structures',
                        'Work on advanced vocabulary'
                    ],
                    recommendations: [
                        'Focus on areas where you made mistakes',
                        'Practice with more B2 level materials',
                        'Study advanced grammar patterns'
                    ]
                };
            } else if (scorePercentage >= 50) {
                levelData = {
                    levelName: 'Developing Skills',
                    levelDescription: 'You are approaching B2 level',
                    levelColor: 'bg-yellow-50 border-yellow-500',
                    levelIcon: '📈',
                    cefr: 'B1+',
                    improvements: [
                        'Focus on fundamental grammar rules',
                        'Expand your vocabulary systematically',
                        'Practice reading comprehension regularly'
                    ],
                    recommendations: [
                        'Review B1 and B2 grammar thoroughly',
                        'Use vocabulary building exercises',
                        'Practice with graded reading materials'
                    ]
                };
            } else {
                levelData = {
                    levelName: 'Needs Improvement',
                    levelDescription: 'Focus on building fundamental skills',
                    levelColor: 'bg-red-50 border-red-500',
                    levelIcon: '📚',
                    cefr: 'B1',
                    improvements: [
                        'Review basic grammar structures',
                        'Build core vocabulary',
                        'Practice with simpler texts first'
                    ],
                    recommendations: [
                        'Start with B1 level materials',
                        'Focus on basic grammar patterns',
                        'Use vocabulary flashcards and apps'
                    ]
                };
            }

            return {
                totalQuestions: totalCount,
                correctAnswers: correctCount,
                incorrectAnswers: totalCount - correctCount,
                scorePercentage: scorePercentage,
                detailedAnswers: detailedAnswers,
                ...levelData
            };
        }

        function generateDetailedReview(reviewData) {
            if (!reviewData.detailedAnswers || reviewData.detailedAnswers.length === 0) {
                return '<p class="text-gray-500 text-center py-8">No answers to review. Complete the test to see detailed feedback.</p>';
            }

            return reviewData.detailedAnswers.map(answer => `
                <div class="p-4 border rounded-lg ${answer.isCorrect ? 'bg-green-50 border-green-500' : 'bg-red-50 border-red-500'}">
                    <div class="flex items-center justify-between mb-3">
                        <h5 class="font-medium text-gray-700">Question ${answer.questionNumber}</h5>
                        <span class="px-3 py-1 rounded-full text-sm font-medium ${answer.isCorrect ? 'bg-green-600 text-white' : 'bg-red-600 text-white'}">
                            ${answer.isCorrect ? '✅ Correct' : '❌ Incorrect'}
                        </span>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                        <div>
                            <span class="text-sm font-medium text-gray-600">Your Answer:</span>
                            <div class="font-medium ${answer.isCorrect ? 'text-green-700' : 'text-red-700'}">${answer.userAnswer}</div>
                        </div>
                        <div>
                            <span class="text-sm font-medium text-gray-600">Correct Answer:</span>
                            <div class="font-medium text-green-700">${answer.correctAnswer}</div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <span class="text-sm font-medium text-gray-600">All Options:</span>
                        <div class="grid grid-cols-2 gap-2 mt-1">
                            ${answer.options.map((option, index) => {
                                const letter = String.fromCharCode(65 + index);
                                const isUserChoice = answer.userAnswer === letter;
                                const isCorrect = answer.correctAnswer === letter;
                                return `
                                    <div class="text-sm p-2 rounded ${isCorrect ? 'bg-green-100 text-green-800' : isUserChoice ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-600'}">
                                        <strong>${letter})</strong> ${option}
                                        ${isCorrect ? ' ✅' : isUserChoice ? ' ❌' : ''}
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>

                    <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                        <span class="text-sm font-medium text-blue-700">Explanation:</span>
                        <p class="text-sm text-blue-800 mt-1">${answer.explanation}</p>
                    </div>
                </div>
            `).join('');
        }

        function exportResults() {
            const reviewData = calculateDetailedResults();
            const exportData = {
                timestamp: new Date().toISOString(),
                topic: generatedContent?.topic || 'Unknown',
                score: `${reviewData.correctAnswers}/${reviewData.totalQuestions} (${reviewData.scorePercentage}%)`,
                level: reviewData.cefr,
                answers: reviewData.detailedAnswers
            };

            const dataStr = JSON.stringify(exportData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `B2-test-results-${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);

            alert('📊 Results exported successfully!\n\nYour detailed test results have been downloaded as a JSON file.');
        }

        function generateCompleteReadingTest() {
            alert('🚧 Complete Reading Test Coming Soon!\n\nThis will include all 7 parts in sequence with proper timing and navigation.');
        }

        function generateCompleteWritingTest() {
            alert('🚧 Complete Writing Test Coming Soon!\n\nThis will include both Essay and Part 2 tasks with proper timing.');
        }

        function generateFallbackWriting(partNumber = 1, taskType = 'essay') {
            // Simple fallback for writing when AI fails
            const topics = [
                'technology in education', 'environmental protection', 'social media impact',
                'healthy lifestyle', 'cultural exchange', 'work-life balance'
            ];

            const selectedTopic = topics[Math.floor(Math.random() * topics.length)];
            const uniqueId = `writing_fallback_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

            generatedContent = {
                generationId: uniqueId,
                partNumber: partNumber,
                taskType: taskType,
                topic: selectedTopic,
                isAIGenerated: false,
                isFallback: true,
                generationTime: 0,
                prompt: `Write a ${taskType} about ${selectedTopic}. Your ${taskType} should be 140-190 words.`,
                instructions: getWritingInstructions(partNumber, taskType)
            };

            console.log('✅ Generated fallback writing content:', generatedContent);
            showGeneratedWriting();
        }

        function getWritingInstructions(partNumber, taskType) {
            if (partNumber === 1) {
                return 'Write an essay giving your opinion on the topic. Use the bullet points to help you organize your ideas.';
            } else {
                const instructions = {
                    article: 'Write an article for a magazine or website. Make it engaging and informative.',
                    email: 'Write an email to a friend or colleague. Use an appropriate tone.',
                    letter: 'Write a formal or semi-formal letter. Include appropriate opening and closing.',
                    report: 'Write a report with clear sections and recommendations.',
                    review: 'Write a review giving your opinion and recommendations.'
                };
                return instructions[taskType] || instructions.article;
            }
        }

        function startNewTest() {
            userAnswers = {};
            generatedContent = null;
            closeModal();
            startFullTest();
        }

        function showComingSoon(module) {
            alert(`🚧 ${module} Module Coming Soon!\n\nThis module is in development and will include:\n• Authentic exercises\n• AI-generated content\n• Realistic exam conditions`);
        }

        // Debug function to test application functionality
        function testApplication() {
            console.log('🧪 Testing B2 First Trainer Application...');

            // Test 1: Check if essential functions exist
            const essentialFunctions = [
                'generateAndShowReading', 'showGeneratedReading', 'renderPartContent',
                'generateFallbackReading', 'saveAnswer', 'saveOpenAnswer', 'saveWordFormationAnswer'
            ];

            const missingFunctions = essentialFunctions.filter(func => typeof window[func] !== 'function');

            if (missingFunctions.length > 0) {
                console.error('❌ Missing functions:', missingFunctions);
                return false;
            }

            console.log('✅ All essential functions exist');

            // Test 2: Check if modal elements exist
            const modalTitle = document.getElementById('modalTitle');
            const modalContent = document.getElementById('modalContent');

            if (!modalTitle || !modalContent) {
                console.error('❌ Modal elements missing');
                return false;
            }

            console.log('✅ Modal elements found');

            // Test 3: Test fallback content generation
            try {
                generateFallbackReading(1);
                console.log('✅ Part 1 fallback generation works');

                generateFallbackReading(2);
                console.log('✅ Part 2 fallback generation works');

                generateFallbackReading(3);
                console.log('✅ Part 3 fallback generation works');

            } catch (error) {
                console.error('❌ Fallback generation failed:', error);
                return false;
            }

            console.log('🎉 Application test completed successfully!');
            return true;
        }

        // Auto-run test on page load (for debugging)
        window.addEventListener('load', function() {
            setTimeout(() => {
                console.log('🚀 B2 First Trainer loaded. Run testApplication() to verify functionality.');
            }, 1000);
        });

        function openModal() {
            document.getElementById('testModal').classList.remove('hidden');
        }

        function closeModal() {
            document.getElementById('testModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('testModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        console.log('Script loaded successfully');
    </script>
</body>
</html>
